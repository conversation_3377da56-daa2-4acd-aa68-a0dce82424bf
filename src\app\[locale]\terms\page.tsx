import React from "react";
import { Separator } from "@/components/ui/separator";
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { Metadata } from "next";

export async function generateMetadata({ params }: { params: { locale: string } }): Promise<Metadata> {
  return {
    title: 'Terms and Conditions',
    description: 'Terms and Conditions for YT Mate',
    robots: {
      index: false,
      follow: false,
    },
  };
}


export default function TermsPage() {
  return (
    <>
    <Header />
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">Terms and Conditions</h1>
      <p className="text-sm text-muted-foreground mb-6">Last Updated: April 15, 2025</p>
      
      <Separator className="mb-6" />
      
      <div className="space-y-6">
        <section>
          <h2 className="text-xl font-semibold mb-3">1. Agreement to Terms</h2>
          <p>
            By accessing or using YT Mate (ytmate.net), you agree to be bound by these Terms and Conditions. If you disagree with any part of these terms, you may not access or use our services.
          </p>
        </section>

        <section>
          <h2 className="text-xl font-semibold mb-3">2. Service Description</h2>
          <p>
            YT Mate provides tools and services related to YouTube content. Our services may include, but are not limited to, video downloading, conversion, and analysis tools. We reserve the right to modify, suspend, or discontinue any part of our services at any time without notice.
          </p>
        </section>

        <section>
          <h2 className="text-xl font-semibold mb-3">3. User Responsibilities</h2>
          <p>
            You agree to use YT Mate only for lawful purposes and in accordance with these Terms. You are responsible for:
          </p>
          <ul className="list-disc pl-6 mt-2 space-y-1">
            <li>Ensuring you have the necessary rights to access, download, or use any content processed through our services</li>
            <li>Complying with YouTube's Terms of Service</li>
            <li>Not using our services for any illegal activities</li>
            <li>Not attempting to disrupt or compromise the security of our platform</li>
          </ul>
        </section>

        <section>
          <h2 className="text-xl font-semibold mb-3">4. Intellectual Property</h2>
          <p>
            YT Mate respects intellectual property rights. Our services are designed to be used with content that you have the legal right to access and download. You acknowledge that:
          </p>
          <ul className="list-disc pl-6 mt-2 space-y-1">
            <li>YT Mate does not claim ownership of any content processed through our services</li>
            <li>You must have the necessary rights or permissions to download or convert any content</li>
            <li>Using our services to infringe upon copyrights or other intellectual property rights is prohibited</li>
          </ul>
        </section>

        <section>
          <h2 className="text-xl font-semibold mb-3">5. Limitation of Liability</h2>
          <p>
            YT Mate and its operators shall not be liable for any indirect, incidental, special, consequential, or punitive damages resulting from your access to or use of, or inability to access or use, the services or any content provided through the services.
          </p>
        </section>

        <section>
          <h2 className="text-xl font-semibold mb-3">6. Disclaimer of Warranties</h2>
          <p>
            Our services are provided "as is" and "as available," without any warranties of any kind, either express or implied. We do not guarantee that our services will always be available, uninterrupted, timely, secure, or error-free.
          </p>
        </section>

        <section>
          <h2 className="text-xl font-semibold mb-3">7. Indemnification</h2>
          <p>
            You agree to indemnify and hold harmless YT Mate, its operators, affiliates, and partners from any claims, damages, liabilities, costs, or expenses arising from your use of our services or violation of these Terms.
          </p>
        </section>

        <section>
          <h2 className="text-xl font-semibold mb-3">8. Governing Law</h2>
          <p>
            These Terms shall be governed by and construed in accordance with applicable international laws, without regard to conflict of law provisions.
          </p>
        </section>

        <section>
          <h2 className="text-xl font-semibold mb-3">9. Changes to Terms</h2>
          <p>
            We reserve the right to modify these Terms at any time. Your continued use of YT Mate after such modifications constitutes your acceptance of the revised Terms. We will make reasonable efforts to notify users of significant changes.
          </p>
        </section>

        <section>
          <h2 className="text-xl font-semibold mb-3">10. Contact Information</h2>
          <p>
            If you have any questions about these Terms, please contact <NAME_EMAIL>.
          </p>
        </section>
      </div>
    </div>
    <Footer />
    </>
  );
}
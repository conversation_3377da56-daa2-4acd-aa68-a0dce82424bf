"use client";

import { Card, CardContent } from "@/components/ui/card";

interface VideoPreviewProps {
  videoId: string;
  title: string;
  thumbnailUrl: string;
  duration: string;
  author?: string;
}

export function VideoPreview({ videoId, title, thumbnailUrl, duration, author }: VideoPreviewProps) {
  // Format duration from seconds to minutes:seconds
  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <Card id="video-preview" className="overflow-hidden mt-6">
      <CardContent className="p-0">
        <div className="relative aspect-video w-full">
          <iframe
            src={`https://www.youtube.com/embed/${videoId}`}
            title={title}
            className="w-full h-full"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          />
          <div className="absolute bottom-2 right-2 bg-black/70 text-white text-sm px-2 py-1 rounded">
            {typeof duration === 'number' ? formatDuration(duration) : duration}
          </div>
        </div>
        <div className="p-4">
          <h2 className="text-xl font-bold line-clamp-2">{title}</h2>
          {author && <p className="text-muted-foreground mt-1">{author}</p>}
        </div>
      </CardContent>
    </Card>
  );
}
"use client";

import { useEffect, useRef, useState } from 'react';

export function ResponsiveAd(): JSX.Element | null {
  const banner = useRef<HTMLDivElement>(null);
  const [isProduction, setIsProduction] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);

  // Desktop ad configuration (728x90)
  const desktopAtOptions = {
    key: '535a465aa6ce62db43bf914803fa8eb1',
    format: 'iframe',
    height: 90,
    width: 728,
    params: {},
  };

  // Mobile ad configuration (320x50)
  const mobileAtOptions = {
    key: '56c41beaeea77a4b99fcbd212ba8acbd',
    format: 'iframe',
    height: 50,
    width: 320,
    params: {},
  };

  useEffect(() => {
    // Check if we're in production environment
    setIsProduction(process.env.NODE_ENV === 'production');
    
    // Check if device is desktop/laptop (screen width >= 1024px)
    const checkIsDesktop = () => {
      setIsDesktop(window.innerWidth >= 1024);
    };
    
    // Initial check
    checkIsDesktop();
    
    // Listen for window resize
    window.addEventListener('resize', checkIsDesktop);
    
    return () => {
      window.removeEventListener('resize', checkIsDesktop);
    };
  }, []);

  useEffect(() => {
    if (banner.current && !banner.current.firstChild && isProduction) {
      // Choose the appropriate ad configuration based on device type
      const atOptions = isDesktop ? desktopAtOptions : mobileAtOptions;
      
      const conf = document.createElement('script');
      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.src = `//www.highperformanceformat.com/${atOptions.key}/invoke.js`;
      conf.innerHTML = `atOptions = ${JSON.stringify(atOptions)}`;

      banner.current.append(conf);
      banner.current.append(script);
    }
  }, [banner, isProduction, isDesktop]);

  // Don't render anything in development
  if (!isProduction) {
    return null;
  }

  return (
    <div className="w-full flex justify-center py-2 bg-muted/30">
      <div 
        className="mx-2 border border-gray-200 justify-center items-center text-white text-center" 
        ref={banner}
      ></div>
    </div>
  );
}

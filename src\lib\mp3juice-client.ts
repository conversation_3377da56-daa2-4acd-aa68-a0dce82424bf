/**
 * MP3Juice client for browser-side video format retrieval
 * Uses proxy to hide cdn.mp3j.cc domain
 */
import axios, { AxiosRequestConfig } from "axios";
import { extractYouTubeId } from "./crypto";

// Define interfaces for MP3Juice API
export interface MP3JuiceFormat {
  quality: string;
  url: string;
  type: string;
  size?: string;
  duration?: string;
}

export interface MP3JuiceVideoInfo {
  title: string;
  thumbnail: string;
  duration: string;
  author: string;
  formats: MP3JuiceFormat[];
  videoId: string;
}

export interface MP3JuiceResponse {
  status: boolean;
  data?: {
    title: string;
    thumbnail: string;
    duration: string;
    author: string;
    formats: MP3JuiceFormat[];
  };
  error?: string;
}

// Convert MP3Juice formats to our standard format interfaces
export interface AudioFormat {
  quality: number;
  url: string | null;
  label: string;
}

export interface VideoFormat {
  height: number;
  width: number;
  url: string | null;
  quality: number;
  label: string;
  default_selected: number;
}

export interface DownloadFormat {
  quality: number;
  label: string;
  url: string | null;
  type: 'video' | 'audio';
  container: string;
  height?: number;
  width?: number;
  sizeInMB?: number;
}

export interface VideoInfo {
  key: string;
  url: string;
  title: string;
  titleSlug: string;
  thumbnail: string;
  duration: number;
  durationLabel: string;
  audio_formats: AudioFormat[];
  video_formats: VideoFormat[];
  default_selected: number;
  fromCache?: boolean;
  videoId?: string;
  author?: string;
}

/**
 * MP3Juice client for browser-side API calls
 */
class MP3JuiceClient {
  private readonly api = {
    base: "/api/mp3juice",
    search: "/search",
  };

  private readonly headers = {
    accept: "*/*",
    "accept-language": "en-GB,en;q=0.9,en-US;q=0.8,en-IN;q=0.7",
    "content-type": "application/x-www-form-urlencoded",
    "origin": "https://mp3juice.lt",
    "priority": "u=1, i",
    "referer": "https://mp3juice.lt/",
    "sec-ch-ua": '"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "cross-site",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0"
  };

  /**
   * Extract video ID from a YouTube URL
   */
  private extractVideoId(url: string): string | null {
    return extractYouTubeId(url);
  }

  /**
   * Generate a unique identifier for the request
   */
  private generateUid(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Convert duration string to seconds
   */
  private parseDuration(duration: string): number {
    if (!duration) return 0;
    
    const parts = duration.split(':').map(Number);
    if (parts.length === 2) {
      return parts[0] * 60 + parts[1]; // MM:SS
    } else if (parts.length === 3) {
      return parts[0] * 3600 + parts[1] * 60 + parts[2]; // HH:MM:SS
    }
    return 0;
  }

  /**
   * Convert MP3Juice formats to our standard format
   */
  private convertFormats(formats: MP3JuiceFormat[]): { audio: AudioFormat[], video: VideoFormat[] } {
    const audio: AudioFormat[] = [];
    const video: VideoFormat[] = [];

    formats.forEach((format, index) => {
      if (format.type === 'audio' || format.type === 'mp3') {
        // Extract quality from format string (e.g., "128kbps" -> 128)
        const qualityMatch = format.quality.match(/(\d+)/);
        const quality = qualityMatch ? parseInt(qualityMatch[1]) : 128;
        
        audio.push({
          quality,
          url: format.url,
          label: `MP3 ${format.quality}`
        });
      } else if (format.type === 'video' || format.type === 'mp4') {
        // Extract quality from format string (e.g., "720p" -> 720)
        const qualityMatch = format.quality.match(/(\d+)/);
        const quality = qualityMatch ? parseInt(qualityMatch[1]) : 360;
        
        video.push({
          height: quality,
          width: Math.round(quality * 16 / 9), // Assume 16:9 aspect ratio
          url: format.url,
          quality,
          label: `MP4 ${format.quality}`,
          default_selected: index === 0 ? 1 : 0
        });
      }
    });

    return { audio, video };
  }

  /**
   * Make an HTTP request to the MP3Juice API
   */
  private async request(url: string, data: any): Promise<any> {
    try {
      const config: AxiosRequestConfig = {
        method: 'post',
        url,
        headers: this.headers,
        data: new URLSearchParams(data).toString(),
      };
      
      const response = await axios(config);
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error("MP3Juice API request error:", {
          url: error.config?.url,
          status: error.response?.status,
          statusText: error.response?.statusText,
        });
      } else {
        console.error("Request error:", error);
      }
      throw new Error(`MP3Juice API request failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get video information from a YouTube URL using MP3Juice API
   */
  public async getVideoInfo(url: string): Promise<VideoInfo> {
    try {
      // Extract video ID
      const videoId = this.extractVideoId(url);
      if (!videoId) throw new Error("Invalid YouTube URL");

      // Prepare request data
      const requestData = {
        q: url,
        _ym_uid: this.generateUid()
      };

      // Make API request
      const response = await this.request(`${this.api.base}${this.api.search}`, requestData);
      
      // Parse response (MP3Juice API response format may vary)
      let videoInfo: MP3JuiceVideoInfo;
      
      if (response && response.data) {
        videoInfo = response.data;
      } else if (response && response.title) {
        videoInfo = response;
      } else {
        throw new Error("Invalid API response format");
      }

      // Convert formats
      const { audio, video } = this.convertFormats(videoInfo.formats || []);

      // Create title slug
      const titleSlug = videoInfo.title
        .toLowerCase()
        .replace(/[^\w\s]/gi, '')
        .replace(/\s+/g, '-');

      // Return standardized format
      return {
        key: videoId,
        url,
        title: videoInfo.title,
        titleSlug,
        thumbnail: videoInfo.thumbnail,
        duration: this.parseDuration(videoInfo.duration),
        durationLabel: videoInfo.duration,
        audio_formats: audio,
        video_formats: video,
        default_selected: 0,
        videoId,
        author: videoInfo.author
      };
    } catch (error) {
      throw new Error(`Failed to get video info from MP3Juice: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get a download URL for a specific format
   */
  public async getDownloadUrl(videoId: string, format: DownloadFormat): Promise<string> {
    try {
      if (!videoId) throw new Error("Video ID is required");
      if (!format) throw new Error("Format is required");
      
      // If URL is already available, return it directly
      if (format.url) return format.url;
      
      throw new Error("Download URL not available for this format");
    } catch (error) {
      console.error("Error getting download URL:", error);
      throw new Error(`Failed to get download URL: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}

// Export a singleton instance
export const mp3juiceClient = new MP3JuiceClient();

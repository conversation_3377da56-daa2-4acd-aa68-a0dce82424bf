/**
 * MP3Juice client for browser-side video format retrieval
 * Uses proxy to hide cdn.mp3j.cc domain
 */
import axios, { AxiosRequestConfig } from "axios";
import { extractYouTubeId } from "./crypto";

// Define interfaces for MP3Juice API response
export interface MP3JuiceSource {
  itag: number;
  mimeType: string;
  bitrate?: number;
  width?: number;
  height?: number;
  quality: string;
  qualityLabel?: string;
  fps?: number;
  url: string;
  audioQuality?: string;
  audioSampleRate?: string;
  audioChannels?: number;
  approxDurationMs?: string;
  contentLength?: string;
}

export interface MP3JuiceVideoData {
  videoId: string;
  title: string;
  viewCount: string;
  source: MP3JuiceSource[];
  exist: boolean;
  getRelatedVideos: Array<{
    id: string;
    title: string;
  }>;
}

export interface MP3JuiceResponse {
  YoutubeVideo: MP3JuiceVideoData;
  source: string;
}

// Convert MP3Juice formats to our standard format interfaces
export interface AudioFormat {
  quality: number;
  url: string | null;
  label: string;
}

export interface VideoFormat {
  height: number;
  width: number;
  url: string | null;
  quality: number;
  label: string;
  default_selected: number;
}

export interface DownloadFormat {
  quality: number;
  label: string;
  url: string | null;
  type: 'video' | 'audio';
  container: string;
  height?: number;
  width?: number;
  sizeInMB?: number;
}

export interface VideoInfo {
  key: string;
  url: string;
  title: string;
  titleSlug: string;
  thumbnail: string;
  duration: number;
  durationLabel: string;
  audio_formats: AudioFormat[];
  video_formats: VideoFormat[];
  default_selected: number;
  fromCache?: boolean;
  videoId?: string;
  author?: string;
}

/**
 * MP3Juice client for browser-side API calls
 */
class MP3JuiceClient {
  private readonly api = {
    search: "/api/mp3juice-proxy",
  };

  private readonly headers = {
    "accept": "*/*",
    "accept-language": "en-GB,en;q=0.9,en-US;q=0.8,en-IN;q=0.7",
    "content-type": "application/x-www-form-urlencoded",
    "origin": "https://mp3juice.lt",
    "referer": "https://mp3juice.lt/",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0"
  };

  /**
   * Extract video ID from a YouTube URL
   */
  private extractVideoId(url: string): string | null {
    return extractYouTubeId(url);
  }

  /**
   * Generate a unique identifier for the request
   */
  private generateUid(): string {
    return Date.now().toString() + Math.random().toString(36).substring(2, 11);
  }



  /**
   * Convert MP3Juice sources to our standard format
   */
  private convertFormats(sources: MP3JuiceSource[]): { audio: AudioFormat[], video: VideoFormat[] } {
    const audio: AudioFormat[] = [];
    const video: VideoFormat[] = [];

    sources.forEach((source, index) => {
      if (source.mimeType.startsWith('audio/')) {
        // Audio format
        let quality = 128; // default
        if (source.audioQuality === 'AUDIO_QUALITY_MEDIUM') {
          quality = 128;
        } else if (source.audioQuality === 'AUDIO_QUALITY_LOW') {
          quality = 64;
        } else if (source.audioQuality === 'AUDIO_QUALITY_ULTRALOW') {
          quality = 32;
        } else if (source.bitrate) {
          quality = Math.round(source.bitrate / 1000); // Convert to kbps
        }

        audio.push({
          quality,
          url: source.url,
          label: `MP3 ${quality}kbps`
        });
      } else if (source.mimeType.startsWith('video/') && source.height) {
        // Video format with height (not audio-only)
        video.push({
          height: source.height,
          width: source.width || Math.round(source.height * 16 / 9),
          url: source.url,
          quality: source.height,
          label: `MP4 ${source.qualityLabel || source.height + 'p'}`,
          default_selected: index === 0 ? 1 : 0
        });
      }
    });

    // Sort by quality (highest first)
    audio.sort((a, b) => b.quality - a.quality);
    video.sort((a, b) => b.quality - a.quality);

    return { audio, video };
  }

  /**
   * Make an HTTP request to the MP3Juice API
   */
  private async request(url: string, data: any): Promise<any> {
    try {
      const config: AxiosRequestConfig = {
        method: 'post',
        url,
        headers: this.headers,
        data: new URLSearchParams(data).toString(),
      };
      
      const response = await axios(config);
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error("MP3Juice API request error:", {
          url: error.config?.url,
          status: error.response?.status,
          statusText: error.response?.statusText,
        });
      } else {
        console.error("Request error:", error);
      }
      throw new Error(`MP3Juice API request failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get video information from a YouTube URL using MP3Juice API
   */
  public async getVideoInfo(url: string): Promise<VideoInfo> {
    try {
      // Extract video ID
      const videoId = this.extractVideoId(url);
      if (!videoId) throw new Error("Invalid YouTube URL");

      // Prepare request data
      const requestData = {
        q: url,
        _ym_uid: this.generateUid()
      };

      // Make API request
      const response = await this.request(this.api.search, requestData);

      // Parse response - MP3Juice returns { YoutubeVideo: {...}, source: "YouTube" }
      if (!response || !response.YoutubeVideo) {
        throw new Error("Invalid API response format");
      }

      const videoData = response.YoutubeVideo;

      // Convert formats
      const { audio, video } = this.convertFormats(videoData.source || []);

      // Calculate duration from approxDurationMs (use first source with duration)
      let duration = 0;
      let durationLabel = "0:00";
      const sourceWithDuration = videoData.source.find((s: MP3JuiceSource) => s.approxDurationMs);
      if (sourceWithDuration) {
        duration = Math.round(parseInt(sourceWithDuration.approxDurationMs) / 1000);
        const minutes = Math.floor(duration / 60);
        const seconds = duration % 60;
        durationLabel = `${minutes}:${seconds.toString().padStart(2, '0')}`;
      }

      // Create title slug
      const titleSlug = videoData.title
        .toLowerCase()
        .replace(/[^\w\s]/gi, '')
        .replace(/\s+/g, '-');

      // Generate thumbnail URL from video ID
      const thumbnail = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;

      // Return standardized format
      return {
        key: videoId,
        url,
        title: videoData.title,
        titleSlug,
        thumbnail,
        duration,
        durationLabel,
        audio_formats: audio,
        video_formats: video,
        default_selected: 0,
        videoId,
        author: '' // MP3Juice doesn't provide author info
      };
    } catch (error) {
      throw new Error(`Failed to get video info from MP3Juice: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get a download URL for a specific format
   */
  public async getDownloadUrl(videoId: string, format: DownloadFormat): Promise<string> {
    try {
      if (!videoId) throw new Error("Video ID is required");
      if (!format) throw new Error("Format is required");
      
      // If URL is already available, return it directly
      if (format.url) return format.url;
      
      throw new Error("Download URL not available for this format");
    } catch (error) {
      console.error("Error getting download URL:", error);
      throw new Error(`Failed to get download URL: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}

// Export a singleton instance
export const mp3juiceClient = new MP3JuiceClient();

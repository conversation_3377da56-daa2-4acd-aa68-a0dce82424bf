// encryption.c - Simplified WebAssembly module for secure key handling
#include <stdlib.h>
#include <string.h>
#include <emscripten.h>

// The actual key - stays inside WebAssembly
static const char SECRET_KEY[] = "C5D58EF67A7584E4A29F6C35BBC4EB12";

// XOR key for additional encryption
static const char XOR_KEY[] = "F8A1B2C3D4E5F6A7B8C9D0E1F2A3B4C5";

// Global variables to store the key buffer
static char* result_buffer = NULL;

// Function to return the key length (for verification)
EMSCRIPTEN_KEEPALIVE
int getSecretKeyLength() {
    return strlen(SECRET_KEY);
}

// Simple XOR process to encrypt/decrypt data
EMSCRIPTEN_KEEPALIVE
char* getEncryptedKey() {
    // Free previous buffer if it exists
    if (result_buffer != NULL) {
        free(result_buffer);
        result_buffer = NULL;
    }
    
    // Allocate new buffer
    int len = strlen(SECRET_KEY);
    result_buffer = (char*)malloc(len + 1);
    
    if (result_buffer == NULL) {
        return "ERROR_MEMORY";
    }
    
    // XOR encrypt the key
    for (int i = 0; i < len; i++) {
        result_buffer[i] = SECRET_KEY[i] ^ XOR_KEY[i % strlen(XOR_KEY)];
    }
    result_buffer[len] = '\0';
    
    return result_buffer;
}

// Test function to verify WebAssembly is working
EMSCRIPTEN_KEEPALIVE
int testWebAssembly() {
    return 42;
}

// Get individual bytes of the key (for when string passing fails)
EMSCRIPTEN_KEEPALIVE
int getKeyByteAt(int index) {
    if (index < 0 || index >= strlen(SECRET_KEY)) {
        return -1;
    }
    return (int)(unsigned char)SECRET_KEY[index];
}

// Alternative: Get key as byte array
EMSCRIPTEN_KEEPALIVE
void* getKeyBytes() {
    int len = strlen(SECRET_KEY);
    char* bytes = (char*)malloc(len);
    if (bytes == NULL) return NULL;
    
    memcpy(bytes, SECRET_KEY, len);
    return bytes;
}

// Export the key byte by byte (very reliable approach)
EMSCRIPTEN_KEEPALIVE
void getKeyByteByByte(char* output) {
    int len = strlen(SECRET_KEY);
    for (int i = 0; i < len; i++) {
        output[i] = SECRET_KEY[i];
    }
    output[len] = '\0';
}
{"layout": {"metadata": {"title": "YT Mate - YouTube Videolarını MP3 ve MP4 Formatlarında İndir", "titleTemplate": "%s | YT Mate", "description": "Videoları MP3 (320/256/192 kbps) ve MP4 (4K/2K/FHD/HD) formatlarına dönüştürmek için ücretsiz YouTube indiricisi. Hızlı, güvenli ve kayıt gerektirmez.", "keywords": "YT Mate, youtube video indir, youtube video indirici, youtube mp3 dönüştürücü, youtube mp4 dön<PERSON>ştürücü, 320 kbps mp3, 4K mp4 indir, yüksek kaliteli youtube dönüştürücü", "author": "YT Mate", "creator": "YT Mate", "publisher": "YT Mate"}, "openGraph": {"title": "YT Mate - YouTube Videolarını MP3 ve MP4 Formatlarında İndir", "description": "Videoları MP3 (320/256/192 kbps) ve MP4 (4K/2K/FHD/HD) formatlarına dönüştürmek için ücretsiz YouTube indiricisi. Hızlı, güvenli ve kayıt gerektirmez.", "siteName": "YT Mate"}, "twitter": {"title": "YT Mate - YouTube Videolarını MP3 ve MP4 Formatlarında İndir", "description": "Videoları MP3 (320/256/192 kbps) ve MP4 (4K/2K/FHD/HD) formatlarına dönüştürmek için ücretsiz YouTube indiricisi. Hızlı, güvenli ve kayıt gerektirmez."}}, "home": {"hero": {"title": "En İyi YouTube Video İndirici", "description": "YouTube'dan premium kalitede videolar indirin. Yüksek kaliteli MP3 ses (320 kbps, 256 kbps, 192 kbps) ve kristal netliğinde MP4 videolar (4K, 2K, Full HD, HD) edinin. Kayıt gerektirmez!"}, "mp3": {"title": "YouTube'dan MP3 Dönüştürücü", "description": "YouTube videolarından çeşitli bit hızlarında yüksek kaliteli ses çıkarın", "descriptionExtra": "Diğer video formatları için video-ses dönüştürücümüzü deneyin", "premium": "Premium Kalite: Audiofil derecesinde ses için 320 kbps MP3", "high": "Yüksek Kalite: <PERSON><PERSON><PERSON><PERSON>l ses netliği için 256 kbps MP3", "standard": "Standart Kali<PERSON>: <PERSON><PERSON><PERSON> boyutu ve kalite dengesi i<PERSON> 192 kbps MP3", "basic": "Temel Kalite: <PERSON><PERSON> kü<PERSON>ük dosya boyutu için 128 kbps MP3", "button": "MP3'e Dönüştür"}, "mp4": {"title": "YouTube'dan MP4 Dönüştürücü", "description": "Cihazınıza uygun çeşitli çözünürlüklerde video indirin", "ultra": "Ultra HD: <PERSON><PERSON> ka<PERSON>de izleme için 4K (2160p)", "quad": "Quad HD: <PERSON><PERSON><PERSON><PERSON> de<PERSON>y i<PERSON> 2K (1440p)", "fullhd": "Full HD: <PERSON><PERSON>liğ<PERSON>e oynatma için 1080p", "hd": "HD: Bant genişliği dostu yüksek çözünürlük için 720p", "standard": "Standart: Eski cihazlar veya yavaş bağlantılar için 480p/360p", "button": "MP4'e Dönüştür"}, "features": {"title": "Neden YT Mate?", "fast": {"title": "Hızlı ve Verimli", "description": "Optimize edil<PERSON>ş dönüştürme motorumuz, dön<PERSON><PERSON>üm sırasında kalite kaybı olmadan indirmelerinizi ışık hızında işler."}, "premium": {"title": "Premium Formatlar", "description": "Audiofil kalitesinde 320 kbps MP3'ten sinema kalitesinde 4K MP4 videolara kadar geniş bir kalite seçeneği arasından seçim yapın."}, "registration": {"title": "Kayıt Yok", "description": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON> veri toplama yok. <PERSON><PERSON><PERSON>, seç ve anında indir."}}, "howto": {"title": "YouTube Videoları Nasıl İndirilir", "step1": {"title": "URL Yapıştır", "description": "YouTube video ba<PERSON><PERSON><PERSON>sını kopyalayın ve yukarıdaki arama çubuğuna yapıştırın"}, "step2": {"title": "Format Seçin", "description": "<PERSON>rcih ettiğiniz formatı (MP3/MP4) ve kalite seçeneğini belirleyin"}, "step3": {"title": "Dönüş<PERSON>ür", "description": "Dönüş<PERSON><PERSON><PERSON> dü<PERSON><PERSON> tıklayın ve işlemin tama<PERSON>lanmasını bekleyin"}, "step4": {"title": "<PERSON><PERSON><PERSON>", "description": "Dönüştürülen dosyayı doğrudan cihazınıza indirin"}}, "faq": {"title": "Sıkça Sorulan Sorular", "q1": {"question": "YT Mate tamamen ücretsiz mi?", "answer": "<PERSON><PERSON>, hizmetimiz gizli maliyetler veya premium özellikler olmadan %100 ücretsizdir. Tüm kalite seçeneklerini ve formatları ücretsiz sunuyoruz."}, "q2": {"question": "YouTube videolarını indirmek yasal mı?", "answer": "<PERSON><PERSON><PERSON><PERSON> kullanım için video indirmek genellikle kabul edilebilir, ancak bunları yeniden dağıtmak veya ticari olarak kullanmak telif hakkı yasalarını ihlal edebilir. Her zaman fikri mülkiyet haklarına saygı gösterin ve yerel düzenlemeleri kontrol edin."}, "q3": {"question": "320 kbps, 256 kbps ve 192 kbps MP3 arasındaki fark nedir?", "answer": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, kaliteyi ve dosya boyutunu etkileyen ses bit hızını temsil eder. 320 kbps en yüksek kaliteyi sunar ancak dosya boyutu büyüktür, 192 kbps ise daha küçük boyutla iyi kalite sağlar. Ses kalitesi ve depolama alanı arasındaki ihtiyacınıza göre seçim yapın."}, "q4": {"question": "YouTube'dan 4K video indirebilir miyim?", "answer": "<PERSON><PERSON>, orijinal YouTube videosu bu çözünürlükte mevcutsa, aracımız Ultra HD 4K (2160p) video indirmelerini destekler."}, "q5": {"question": "Bazı videoları neden indiremiyorum?", "answer": "Bazı videolar telif hakkı kısıtlamaları, b<PERSON><PERSON><PERSON> sınırlamalar veya YouTube'un içerik koruması tarafından korunabilir. <PERSON>zel videolar, premium içerik veya yaş sınırlamalı videolar indirme için mevcut olmayabilir."}}}, "nav": {"home": "<PERSON>", "ytToMp3": "YT'den MP3", "ytToMp4": "YT'den MP4", "about": "Hakkında", "faq": "SSS"}, "ytToMp3": {"metadata": {"title": "YouTube'dan MP3 Dönüştürücü | Yüksek Kalite 320/256/192 kbps - YT Mate", "description": "YouTube videolarını premium ses kalitesinde (320/256/192 kbps) MP3'e dönüştürün. Kayıt gerektirmeyen ücretsiz, hızlı ve kullanımı kolay. Sınırsız MP3 dosyası indirin.", "keywords": "youtube mp3 dö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, youtube mp3 indir, youtube mp3 320kbps, yüksek kalite mp3 dö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, youtube ses indirici, ücretsiz mp3 dö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "hero": {"title": "YouTube'dan MP3 Dönüştürücü", "description": "Her<PERSON><PERSON> bir YouTube videosundan yüksek kaliteli MP3 ses çıkarın. Audiofil derecesinde 320 kbps, yüksek kaliteli 256 kbps veya standart 192 kbps formatları arasından seçim yapın. Ücretsiz, hızlı ve kayıt gerektirmez."}, "search": {"instruction": "MP3 formatında dönüştürmek ve indirmek için bir YouTube URL'si girin"}, "quality": {"title": "Mevcut MP3 <PERSON><PERSON>", "premium": {"badge": "Premium", "title": "320 kbps MP3", "description": "Audiofil derecesinde ses kalitesi", "feature1": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>de ses", "feature2": "Müzik arşivleme için en iyisi", "feature3": "~2.4 MB / dakika", "button": "320 kbps Seç"}, "high": {"badge": "<PERSON><PERSON><PERSON><PERSON>", "title": "256 kbps MP3", "description": "Mükemmel ses netliği", "feature1": "<PERSON><PERSON><PERSON><PERSON>", "feature2": "<PERSON><PERSON> kalite-boyut dengesi", "feature3": "~2 MB / dakika", "button": "256 kbps Seç"}, "standard": {"badge": "<PERSON><PERSON>", "title": "192 kbps MP3", "description": "<PERSON><PERSON> se<PERSON> ka<PERSON>", "feature1": "Podcast'ler <PERSON><PERSON><PERSON>l", "feature2": "<PERSON><PERSON><PERSON>u", "feature3": "~1.5 MB / dakika", "button": "192 kbps Seç"}, "basic": {"badge": "Temel", "title": "128 kbps MP3", "description": "<PERSON>ha küçük dosya boyutu", "feature1": "Konuşma içeriği için iyi", "feature2": "Hızlı indirmeler", "feature3": "~1 MB / dakika", "button": "128 kbps Seç"}}, "howItWorks": {"title": "YouTube'dan MP3 Dönüştürücümüz Nasıl Çalışır", "step1": {"title": "YouTube URL'sini Yapıştırın", "description": "Tarayıcını<PERSON>n YouTube video bağlantısını kopyalayın ve yukarıdaki arama çubuğuna yapıştırın. <PERSON><PERSON><PERSON><PERSON><PERSON>, oynatma listeleri ve kısa videolar dahil tüm geçerli YouTube URL'lerini kabul eder."}, "step2": {"title": "MP3 <PERSON><PERSON><PERSON>", "description": "Seçeneklerimizden tercih ettiğiniz ses kalitesini seçin: 320 kbps (Premium), 256 kbps (Yüksek), 192 kbps (Standart) veya 128 kbps (Temel). Daha yüksek bit hızları daha iyi ses kalitesi ancak daha büyük dosyalar sunar."}, "step3": {"title": "MP3'ünüzü İndirin", "description": "İndir dü<PERSON><PERSON><PERSON> tıklayın ve MP3 dosyanız saniyeler içinde hazır olacaktır. Kalite kaybı veya filigran olmadan doğrudan cihazınıza kaydedin. Kayıt veya yazılım kurulumu gerekmez."}}, "benefits": {"title": "Neden YouTube'dan MP3 Dönüştürücümüzü Seçmelisiniz?", "quality": {"title": "Yüksek Kaliteli Ses Çıkarma", "description": "Dönüş<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, YouTube videolarından orijinal ses kalitesini koruyarak kristal netliğinde sesle 320 kbps'ye kadar MP3 dosyaları sunar. Üstün ses kalitesine değer veren müzikseverler için idealdir.", "descriptionExtra": "MP4'ü MP3'e dönüştürmeniz mi gerekiyor? <PERSON>zel aracımızı deneyin."}, "unlimited": {"title": "Sınırsız Ücretsiz Dönüşüm", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sı<PERSON>, g<PERSON><PERSON> ücret yok. <PERSON>te veya hızdan ödün vermeden, tamamen ücretsiz olarak istediğiniz kadar YouTube videosunu MP3'e dönüştürün ve indirin."}, "speed": {"title": "Hızlı İşleme Süresi", "description": "Optimize edil<PERSON>ş sunucularımız dönüştürme isteklerinizi anında işler. Uzun videolar bile saniyeler içinde MP3'e dönüştürülerek, diğer dönüştürücülere kıyasla size değerli zaman kazandırır."}, "privacy": {"title": "<PERSON>", "description": "Dönüştürdüğünüz dosyaları saklamayız veya etkinliğinizi takip etmeyiz. YouTube'dan MP3 dönüşümleriniz gizli kalır, maks<PERSON><PERSON> güvenlik için dosyalar indirildikten sonra sunucularımızdan silinir."}}, "faq": {"title": "Sıkça Sorulan Sorular", "q1": {"question": "Seçilecek en iyi MP3 kalitesi hangisidir?", "answer": "Ses kalitesinin önemli olduğu müzik ve şarkılar için, premium ses için 320 kbps öneririz. Pod<PERSON><PERSON>ler, sesli kitaplar veya konuşma içeriği için, 192 kbps depolama alanından tasarruf sağlarken mükemmel netlik sağlar. Dosya boyutu konusunda endişeleniyorsanız, 128 kbps günlük dinleme için uygundur."}, "q2": {"question": "YouTube oynatma listelerini MP3'e dönüştürebilir miyim?", "answer": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>rücümüz YouTube oynatma listesi URL'lerini destekler. Oynatma listesi bağlantısını yapıştırarak bir oynatma listesindeki birden fazla videoyu MP3 formatına dönüştürebilirsiniz. Her video indirmek için ayrı ayrı işlenir."}, "q3": {"question": "MP3 dosyaları doğru meta verilere sahip olacak mı?", "answer": "Dönüş<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, YouTube videosundan mevcut olduğunda başlık ve sanatçı gibi temel meta verileri çıkarmaya ve dahil etmeye çalışır. Daha eksiksiz meta veriler için, indirdikten sonra bir MP3 etiket düzenleyicisi kullanarak dosya özelliklerini düzenlemeniz gerekebilir."}, "q4": {"question": "Dönüştürebileceğim video sayısında bir sınır var mı?", "answer": "<PERSON><PERSON><PERSON>, MP3'e dönüştürebileceğiniz YouTube videoları sayısına herhangi bir sınır koymuyoruz. Hizmetimizi istediğiniz kadar sık kullanabilirsiniz, <PERSON><PERSON><PERSON> kı<PERSON><PERSON>tl<PERSON> olmadan."}}, "cta": {"title": "YouTube'dan MP3'e Dönüştürmeye Hazır mısınız?", "description": "YouTube URL'nizi yukar<PERSON><PERSON>i arama çubuğuna yapıştırarak hemen başlayın. Ücretsiz dönüştürücümüz her zaman kayıt gerektirmeden kullanılabilir.", "disclaimer": "Not: Lütfen içeriği indirme ve dönüştürme hakkına sahip olduğunuzdan emin olun. YT Mate, erişim izniniz olan içeriğin kişisel kullanımı için ta<PERSON>lanmıştır."}}, "ytToMp4": {"metadata": {"title": "YouTube'dan MP4 Dönüştürücü | 4K, 2K, 1080p, 720p Video İndirici - YT Mate", "description": "YouTube videolarını birden fazla kalite seçeneğiyle (4K, 2K, 1080p, 720p) MP4 formatında indirin. Kayıt gerektirmeyen ücretsiz çevrimiçi dönüştürücü. Hızlı ve kullanımı kolay.", "keywords": "youtube mp4 dön<PERSON>şt<PERSON>r<PERSON><PERSON><PERSON>, youtube video indirici, youtube mp4 4k, youtube mp4 dönüştürücü, youtube video indir, 4k video indirici, yüks<PERSON> kaliteli video dönüştürü<PERSON>ü"}, "hero": {"title": "YouTube'dan MP4 Dönüştürücü", "description": "YouTube videolarını premium kalite seçenekleriyle MP4 formatında indirin. Ultra HD 4K, Quad HD 2K, Full HD 1080p ve daha fazlasından seçim yapın. Ücretsiz, hızlı ve kayıt gerektirmez."}, "search": {"instruction": "MP4 formatında dönüştürmek ve indirmek için bir YouTube URL'si girin"}, "quality": {"premium": {"title": "Premium Video Kalite Seçenekleri", "ultra": {"badge": "Ultra HD", "title": "4K (2160p)", "description": "Sinema kalitesinde çözünürlük", "feature1": "Olağanüstü detay", "feature2": "Büyük ekranlar için ideal", "feature3": "~350 MB / dakika", "button": "4K Kalitesini Seç"}, "quad": {"badge": "Quad HD", "title": "2K (1440p)", "description": "<PERSON><PERSON><PERSON><PERSON> resim kalitesi", "feature1": "Üstün kes<PERSON>", "feature2": "Monit<PERSON><PERSON><PERSON> i<PERSON><PERSON>", "feature3": "~200 MB / dakika", "button": "2K Kalitesini Seç"}, "fullhd": {"badge": "Full HD", "title": "1080p", "description": "<PERSON><PERSON>ğ<PERSON> kalite", "feature1": "<PERSON>art y<PERSON><PERSON><PERSON> kalite", "feature2": "Evrensel uyumluluk", "feature3": "~120 MB / dakika", "button": "1080p <PERSON><PERSON><PERSON>"}}, "standard": {"title": "Standart Video Kalite <PERSON>ri", "hd": {"badge": "HD Ready", "title": "720p", "description": "<PERSON><PERSON> ka<PERSON>de video", "feature1": "<PERSON><PERSON><PERSON>", "feature2": "<PERSON><PERSON> ka<PERSON>, daha küçük boyut", "feature3": "~70 MB / dakika", "button": "720p <PERSON><PERSON><PERSON>"}, "sd": {"badge": "SD", "title": "480p", "description": "<PERSON><PERSON>", "feature1": "Eski cihazlar için iyi", "feature2": "Küçük dosya boyutu", "feature3": "~30 MB / dakika", "button": "480p <PERSON><PERSON><PERSON>"}, "low": {"badge": "Düşük", "title": "360p/240p", "description": "Temel çözünürlük", "feature1": "En küçük dosya boyutu", "feature2": "Yavaş bağlantılar için", "feature3": "~15 MB / dakika", "button": "360p/240p <PERSON><PERSON><PERSON>"}}}, "howItWorks": {"title": "YouTube Videoları MP4 Formatında Nasıl İndirilir", "step1": {"title": "URL'yi Ko<PERSON> ve Yapıştır", "description": "YouTube video bağlantısını tarayıcınızdan kopyalayın ve arama kutusuna yapıştırın. Kısa videolar ve canlı yayınlar dahil tüm YouTube bağlantılarıyla çalışır."}, "step2": {"title": "MP4 <PERSON><PERSON><PERSON>", "description": "Kalite seçeneklerimizden tercih ettiğiniz çözünürlüğü seçin. Daha iyi kalite için daha yüksek çözünürlük (4K, 2K) veya daha küçük dosyalar için daha düşük çözünürlük (720p, 480p) seçin."}, "step3": {"title": "<PERSON><PERSON><PERSON> ve <PERSON>fini <PERSON>", "description": "Dönüştürmeyi başlatmak için indirme düğmesine tıklayın. İşlem tamamlandığında, MP4 dosyanız doğrudan cihazınıza kaydedilmeye hazır olacak. Kayıt gerekmez."}}, "devices": {"title": "Cihazınız İçin En İyi MP4 Kalitesi", "recommendationLabel": "Öneri:", "mobile": {"title": "Akıllı Telefonlar ve Tabletler", "device1": {"name": "iPhone/iPad:", "quality": "1080p/720p"}, "device2": {"name": "Android Cihazlar:", "quality": "1080p/720p"}, "device3": {"name": "E<PERSON> Tabletler:", "quality": "720p/480p"}, "recommendation": "720p çoğu mobil cihaz için kalite ve depolama alanı açısından en iyi dengeyi sunar."}, "computer": {"title": "Bilgisayarlar ve Dizüstü Bilgisayarlar", "device1": {"name": "Üst Düzey Ekranlar:", "quality": "4K/2K"}, "device2": {"name": "<PERSON><PERSON>:", "quality": "1080p"}, "device3": {"name": "<PERSON><PERSON> Bilgisayarlar:", "quality": "720p/480p"}, "recommendation": "1080p, dosya boyutlarını makul tutarken çoğu bilgisayar ekranı için iyi çalışır."}, "tv": {"title": "TV'ler ve Büyük Ekranlar", "device1": {"name": "4K Akıllı TV'ler:", "quality": "4K (2160p)"}, "device2": {"name": "Full HD TV'ler:", "quality": "1080p"}, "device3": {"name": "Projektörler:", "quality": "2K/1080p"}, "recommendation": "En iyi görüntüleme deneyimi için her zaman TV'nizin yerel çözünürlü<PERSON><PERSON><PERSON> eş<PERSON>ştirin."}}, "benefits": {"title": "Neden YouTube'dan MP4 Dönüştürücümüzü Seçmelisiniz", "conversion": {"title": "Hızlı Dönüştürme", "description": "Gelişmiş sunucularımız 4K videoları bile saniyeler içinde işler. Yavaş dönüştürücüler için artık beklemeyin - MP4 dosyalarınızı neredeyse anında alın."}, "security": {"title": "<PERSON><PERSON><PERSON><PERSON> ve <PERSON>", "description": "İndirmeleriniz tamamen gizlidir. Dosyalarınızı saklamıyor, etkinliğinizi takip etmiyor veya herhangi bir kişisel bilgi talep etmiyoruz."}, "free": {"title": "Tamamen Ücretsiz", "description": "4K Ultra HD'den standart çözünürlüğe kadar tüm kalite seçeneklerine abonelik ücreti olmadan er<PERSON>şin. Asla gizli ücretler yok."}, "audio": {"title": "Orijinal Ses <PERSON>", "description": "Tüm MP4 indirmeleri yüksek kaliteli ses parçaları içerir. Müzik <PERSON>, öğreticiler ve podcast'ler orijinal ses netliklerini korur.", "descriptionExtra": "Diğer formatlardan video-ses dönüştürme için ek araçlarımızı keşfedin."}}, "faq": {"title": "Sıkça Sorulan Sorular", "q1": {"question": "YouTube videolarını MP4'e indirmek yasal mı?", "answer": "<PERSON><PERSON><PERSON><PERSON> kullanım için video indirmek genellikle kabul edilebilir. <PERSON><PERSON><PERSON>, izin almadan içeriği ye<PERSON>, satmak veya ticari olarak kullanmak telif hakkı yasalarını ihlal edebilir. Her zaman fikri mülkiyet haklarına saygı gösterin ve yerel düzenlemeleri kontrol edin."}, "q2": {"question": "<PERSON><PERSON><PERSON> bir YouTube videosunu 4K kalitesinde indirebilir miyim?", "answer": "Orijinal YouTube videosu 4K çözünürlükte yüklenmişse videoları 4K kalitesinde indirebilirsiniz. Dönüştürücümüz otomatik olarak mevcut en yüksek kaliteyi algılar ve her video için tüm olası çözünürlük seçeneklerini gösterir."}, "q3": {"question": "Bir YouTube videosunu MP4'e dönüştürmek ne kadar sürer?", "answer": "<PERSON><PERSON><PERSON><PERSON>, video uzunluğuna ve seçilen kaliteye bağlı olarak 10-60 saniye arasında sürer. Daha uzun videolar veya daha yüksek çözünürlükler (4K gibi) işlenmek için biraz daha uzun sürebilir."}, "q4": {"question": "MP4 dosyaları doğru sese sahip olacak mı?", "answer": "<PERSON><PERSON>, tüm MP4 indirmeleri hem video hem de ses parçalarını içerir ve tıpkı orijinal YouTube videosunda olduğu gibi mükemmel şekilde senkronize edilmiştir. Ayrı ses dosyaları indirmenize veya birleştirmenize gerek yoktur."}, "q5": {"question": "4K, 2K ve 1080p arasındaki fark nedir?", "answer": "Bu terimler video çözünürlüğünü (ekranda görüntülenen piksel sayısını) ifade eder: 4K (2160p) 3840×2160 piksel, 2K (1440p) 2560×1440 piksel ve 1080p (Full HD) 1920×1080 piksel içerir. Daha yüksek çözünürlükler daha fazla detay ve netlik sağlar ancak daha büyük dosya boyutlarına neden olur."}}, "cta": {"title": "YouTube Videoları İndirmeye Hazır mısınız?", "description": "YouTube URL'nizi yukar<PERSON><PERSON>i arama çubuğuna yapıştırarak hemen başlayın. Ücretsiz dönüştürücümüz kayıt gerektirmeden her zaman kullanılabilir.", "disclaimer": "Not: YT Mate telif hakkı yasalarına saygı duyar. Lütfen yalnızca hakkınız olduğu durumlarda kişisel kullanım için video indirin. Dönüştürülen hiçbir içeriği sunucularımızda barındırmıyoruz."}}, "downloadButton": {"download": "<PERSON><PERSON><PERSON>", "downloadWithFormat": "{format} İndir", "preparing": "İndirme hazırlanıyor...", "downloading": "İndiriliyor...", "downloaded": "{size} indirildi", "cancel": "İndirmeyi İptal Et", "errors": {"default": "İndirme başarısız oldu. Lütfen tekrar deneyin.", "network": "Ağ hatası. Lütfen internet bağlantınızı kontrol edin ve tekrar deneyin.", "memory": "Tarayıcınız bellek yetersizliği yaşadı. Daha küçük bir dosya deneyin veya tarayıcınızı yeniden başlatın.", "cancelled": "İndirme iptal edildi.", "url": "İndirme URL'si alınamadı. Lütfen farklı bir format deneyin."}}, "searchBar": {"placeholder": "YouTube URL'sini buraya yapıştırın...", "search": "Ara", "loading": "Yükleniyor"}, "searchWrapper": {"loading": "Video bilgileri alınıyor...", "errorDefault": "Bilinmeyen bir hata o<PERSON>", "formats": {"video": "{quality}p MP4", "audio": "{quality} kbps MP3"}}, "formatSelector": {"placeholder": "Format seçin", "videoFormats": "Video Formatları", "audioFormats": "Ses Formatları", "selectedFormat": {"type": "Tip", "quality": "<PERSON><PERSON>", "video": "Video", "audio": "Ses"}}, "footer": {"copyright": "© {year} YT Mate. Tüm hakları saklıdır.", "terms": "Şartlar", "privacy": "Gizlilik", "dmca": "DMCA"}}
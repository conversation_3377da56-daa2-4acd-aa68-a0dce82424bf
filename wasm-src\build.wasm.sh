#!/bin/bash
# Simplified build script for WebAssembly

# Create output directory
mkdir -p ../public/wasm

# Compile with minimal options to avoid optimization issues
emcc yt.c \
    -o ../public/wasm/yt.js \
    -s WASM=1 \
    -s EXPORTED_FUNCTIONS="['_getEncryptedKey', '_getSecretKeyLength', '_testWebAssembly', '_getKeyByteAt', '_getKeyBytes', '_getKeyByteByByte', '_malloc', '_free']" \
    -s EXPORTED_RUNTIME_METHODS="['ccall', 'cwrap', 'UTF8ToString', 'writeArrayToMemory', 'getValue', 'setValue']" \
    -s ALLOW_MEMORY_GROWTH=1 \
    -O0 \
    -g \
    -s MODULARIZE=1 \
    -s 'EXPORT_NAME="createEncryptionModule"'

echo "WebAssembly module built successfully!"
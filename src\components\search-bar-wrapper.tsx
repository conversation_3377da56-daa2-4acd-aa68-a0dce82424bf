"use client";

import { useState } from "react";
import { SearchBar } from "./search-bar";
import { VideoPreview } from "./video-preview";
import { FormatSelector } from "./format-selector";
import { DownloadButton } from "./download-button";
import { ResponsiveAd } from "./ads/responsive-ad";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2 } from "lucide-react";
import { mp3juiceClient, VideoInfo as MP3JuiceVideoInfo, DownloadFormat } from "@/lib/mp3juice-client";
import { youtubeClient, VideoInfo } from "@/lib/youtube-client";
import { useTranslations } from 'next-intl';

export function SearchBarWrapper() {
  const t = useTranslations('searchWrapper');

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [videoInfo, setVideoInfo] = useState<MP3JuiceVideoInfo | null>(null);
  const [selectedFormat, setSelectedFormat] = useState<DownloadFormat | null>(null);

  const handleSearch = async (url: string) => {
    setIsLoading(true);
    setError(null);
    setVideoInfo(null);
    setSelectedFormat(null);

    try {
      // Try MP3Juice client first
      let info: MP3JuiceVideoInfo;
      try {
        info = await mp3juiceClient.getVideoInfo(url);
      } catch (mp3juiceError) {
        console.warn('MP3Juice failed, trying YouTube client:', mp3juiceError);
        // Fallback to YouTube client
        const ytInfo = await youtubeClient.getVideoInfo(url);
        // Convert YouTube client format to MP3Juice format
        info = {
          ...ytInfo,
          videoId: ytInfo.videoId || '',
          author: '' // YouTube client doesn't have author field
        };
      }

      setVideoInfo(info);

      // Auto-select the best available format
      if (info.audio_formats && info.audio_formats.length > 0) {
        const bestAudio = info.audio_formats[0];
        setSelectedFormat({
          quality: bestAudio.quality,
          label: bestAudio.label,
          url: bestAudio.url,
          type: 'audio',
          container: 'mp3'
        });
      } else if (info.video_formats && info.video_formats.length > 0) {
        const bestVideo = info.video_formats[0];
        setSelectedFormat({
          quality: bestVideo.quality,
          label: bestVideo.label,
          url: bestVideo.url,
          type: 'video',
          container: 'mp4',
          height: bestVideo.height,
          width: bestVideo.width
        });
      }

    } catch (err) {
      console.error('Error fetching video info:', err);
      setError(err instanceof Error ? err.message : t('errorDefault'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleFormatSelect = (format: DownloadFormat) => {
    setSelectedFormat(format);
  };

  // Get formats from video info
  const videoFormats: DownloadFormat[] = videoInfo ?
    videoInfo.video_formats.map(format => ({
      quality: format.quality,
      label: format.label,
      url: format.url,
      type: 'video' as const,
      container: 'mp4',
      height: format.height,
      width: format.width
    })) : [];

  const audioFormats: DownloadFormat[] = videoInfo ?
    videoInfo.audio_formats.map(format => ({
      quality: format.quality,
      label: format.label,
      url: format.url,
      type: 'audio' as const,
      container: 'mp3'
    })) : [];

  return (
    <div className="container mx-auto max-w-3xl px-4 py-8">
      <SearchBar onSearch={handleSearch} isLoading={isLoading} />
      
      {isLoading && (
        <div className="flex justify-center my-12">
          <div className="flex flex-col items-center">
            <Loader2 className="h-10 w-10 animate-spin text-primary" />
            <p className="mt-4 text-muted-foreground">{t('loading')}</p>
          </div>
        </div>
      )}
      
      {error && (
        <Alert variant="destructive" className="my-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      {videoInfo && (
        <div className="space-y-6">
          <VideoPreview
            videoId={videoInfo.videoId || ''}
            title={videoInfo.title}
            thumbnailUrl={videoInfo.thumbnail}
            duration={videoInfo.durationLabel}
            author={videoInfo.author || ''}
          />

          <FormatSelector
            videoFormats={videoFormats}
            audioFormats={audioFormats}
            onFormatSelect={handleFormatSelect}
            selectedFormatId={selectedFormat?.quality || null}
          />

          <DownloadButton
            selectedFormat={selectedFormat}
            videoTitle={videoInfo.title}
            videoId={videoInfo.videoId || ''}
          />

          <ResponsiveAd />

        </div>
      )}
    </div>
  );
}
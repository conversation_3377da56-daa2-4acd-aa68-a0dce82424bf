{"name": "yt-n-14", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postbuild": "next-sitemap"}, "dependencies": {"@next/third-parties": "^15.3.0", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.487.0", "next": "14.2.26", "next-http-proxy-middleware": "^1.2.7", "next-intl": "^4.0.2", "next-sitemap": "^4.2.3", "react": "^18", "react-dom": "^18", "tailwind-merge": "^3.1.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}
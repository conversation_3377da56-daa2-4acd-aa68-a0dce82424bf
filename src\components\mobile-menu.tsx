"use client";

import { useTranslations } from "next-intl";
import {
  Sheet,
  <PERSON><PERSON><PERSON>lose,
  <PERSON>et<PERSON>ontent,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import { Menu } from "lucide-react";
import { menuItems } from "@/data/data";
import { Link } from "@/i18n/navigation";

export function MobileMenuSheet() {
  const t = useTranslations();
  
  return (
    <Sheet>
      <SheetTrigger asChild>
          <Menu className="h-6 w-6" />
      </SheetTrigger>
      <SheetContent side="right" className="w-[70%] sm:w-[350px] pt-10">
        <SheetHeader className="mb-6">
          <SheetTitle className="text-xl font-bold">YT Mate</SheetTitle>
        </SheetHeader>
        <nav>
          <ul className="flex flex-col">
            {menuItems.map((item) => (
              <li key={item.href}>
                <SheetClose asChild>
                  <Link 
                    href={item.href} 
                    className="block py-2 px-3 rounded-md hover:bg-secondary transition-colors duration-200"
                  >
                    {t(item.label)}
                  </Link>
                </SheetClose>
              </li>
            ))}
          </ul>
        </nav>
      </SheetContent>
    </Sheet>
  );
}
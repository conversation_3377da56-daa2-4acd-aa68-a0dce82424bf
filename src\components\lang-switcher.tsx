'use client';

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { LANGUAGES } from '@/lib/constants';
import { useLocale } from 'next-intl';
import { usePathname, useRouter } from 'next/navigation';


const getSearchQuery = () => {
  const params = new URLSearchParams(window.location.search);
  let searchQuery = '';

  params.forEach((value, key) => {
    if (!key) return;
    if (!searchQuery) {
      searchQuery += '?';
    } else {
      searchQuery += '&';
    }
    searchQuery += `${key}=${value}`;
  });

  return searchQuery;
};

export function LanguageSwitcher() {
  const pathname = usePathname();
  const router = useRouter();
  const locale = useLocale();

  const changeLanguage = (locale: string) => {
    const searchQuery = getSearchQuery();
    const newPathname = `/${locale}${pathname.slice(3)}${searchQuery}`;
    router.push(newPathname);
  };

  return (
    <Select value={locale} onValueChange={(value) => changeLanguage(value)}>
      <SelectTrigger className="w-[100px] p-0">
        <SelectValue placeholder="Language" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          {LANGUAGES.map((language) => (
            <SelectItem
              key={language.value}
              value={language.value}
              className="p-0"
            >
              <SelectLabel>{language.label}</SelectLabel>
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
}
"use client";

import { useTranslations } from "next-intl";
import { LanguageSwitcher } from "./lang-switcher";
import { MobileMenuSheet } from "./mobile-menu";
import { menuItems } from "@/data/data";
import { Link } from "@/i18n/navigation";

export function Header() {
  const t = useTranslations();

  return (
    <header className="bg-white text-gray-800 py-4 sticky top-0 z-50 shadow-md">
      <div className="container mx-auto px-4">
        {/* Desktop and Mobile Nav Container */}
        <div className="flex items-center justify-between">
          <Link href="/" className="text-xl md:text-2xl font-bold flex items-center">
            <span className="hidden sm:inline">YT Mate</span>
            <span className="sm:hidden">YT Mate</span>
          </Link>

          {/* Desktop Nav and Language Switcher - moved to right side */}
          <div className="hidden md:flex items-center space-x-6">
            <nav>
              <ul className="flex space-x-6">
                {menuItems.map((item) => (
                  <li key={item.href}>
                    <Link 
                      href={item.href} 
                      className="hover:text-gray-600 font-medium transition-colors duration-200"
                    >
                      {t(item.label)}
                    </Link>
                  </li>
                ))}
              </ul>
            </nav>
            <LanguageSwitcher />
          </div>

          {/* Mobile Controls - Language Switcher and Menu */}
          <div className="flex items-center space-x-2 md:hidden">
            <LanguageSwitcher />
            <MobileMenuSheet />
          </div>
        </div>
      </div>
    </header>
  );
}
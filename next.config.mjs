/** @type {import('next').NextConfig} */
import nextIntl from 'next-intl/plugin';

const withNextIntl = nextIntl({
  i18n: {
    locales: ['en'],
    defaultLocale: 'en',
    localeDetection: true,
  }
});

const nextConfig = withNextIntl({
  reactStrictMode: true,
  async rewrites() {
    return [
      {
        source: "/api/get-cdn",
        destination: "https://media.savetube.me/api/random-cdn",
      },
      {
        source: "/api/mp3juice/:path*",
        destination: "https://cdn.mp3j.cc/:path*",
      },
      // {
      //   source: "/api/:path*",
      //   destination: "https://media.savetube.me/api/:path*",
      // },
    ];
   },
  images: {
      remotePatterns: [
        {
          protocol: 'https',
          hostname: '**'
        },
      ],
    },
});

export default nextConfig;

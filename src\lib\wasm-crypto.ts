/**
 * WebAssembly crypto module - simplified approach
 * Multiple methods to retrieve the key to ensure compatibility
 */
import { decryptAES128CBC } from './crypto';

// WebAssembly module interface
interface WasmModule {
  _getEncryptedKey: () => number;
  _getSecretKeyLength: () => number;
  _testWebAssembly: () => number;
  _getKeyByteAt: (index: number) => number;
  _getKeyBytes: () => number;
  _getKeyByteByByte: (outputPtr: number) => void;
  _malloc: (size: number) => number;
  _free: (ptr: number) => void;
  ccall: (name: string, returnType: string, argTypes: string[], args: any[]) => any;
  cwrap: (name: string, returnType: string, argTypes: string[]) => (...args: any[]) => any;
  UTF8ToString: (ptr: number) => string;
  setValue: (ptr: number, value: any, type: string) => void;
  getValue: (ptr: number, type: string) => any;
  writeArrayToMemory: (array: ArrayLike<number>, ptr: number) => void;
  HEAPU8: Uint8Array;
}

// Extend window with our module creator
declare global {
  interface Window {
    createEncryptionModule?: (moduleOverrides?: any) => Promise<WasmModule>;
  }
}

/**
 * Manages WebAssembly crypto operations
 */
class WasmCrypto {
  private module: WasmModule | null = null;
  private isInitialized = false;
  private initPromise: Promise<void> | null = null;
  
  // XOR key for additional protection layer
  private readonly xorKey = "F8A1B2C3D4E5F6A7B8C9D0E1F2A3B4C5";
  
  // The raw key - constructed byte by byte if needed
  private secretKey: string | null = null;

  /**
   * Initialize the WebAssembly module
   */
  async init(): Promise<void> {
    if (this.isInitialized) return Promise.resolve();
    if (this.initPromise) return this.initPromise;
    
    this.initPromise = new Promise<void>((resolve, reject) => {
      // Load the WebAssembly script
      if (!window.createEncryptionModule) {
        const script = document.createElement('script');
        script.src = '/wasm/yt.js';
        script.async = true;
        
        script.onload = () => {
          if (!window.createEncryptionModule) {
            return reject(new Error('WebAssembly module not loaded properly'));
          }
          this.initializeModule()
            .then(resolve)
            .catch(reject);
        };
        
        script.onerror = () => {
          reject(new Error('Failed to load WebAssembly module'));
        };
        
        document.body.appendChild(script);
      } else {
        this.initializeModule()
          .then(resolve)
          .catch(reject);
      }
    });
    
    return this.initPromise;
  }
  
  /**
   * Initialize the module once loaded
   */
  private async initializeModule(): Promise<void> {
    try {
      if (!window.createEncryptionModule) {
        throw new Error('WebAssembly module not available');
      }
      
      this.module = await window.createEncryptionModule();
      
      // Test if the module works
      const testResult = this.module._testWebAssembly();
      if (testResult !== 42) {
        throw new Error(`WebAssembly test failed, got ${testResult} expected 42`);
      }
      
      // Verify key length is correct
      const keyLength = this.module._getSecretKeyLength();
      if (keyLength !== 32) {
        throw new Error(`Invalid key length: ${keyLength}, expected 32`);
      }
      
      
      // Pre-retrieve the key
      this.secretKey = await this.retrieveKey();
      
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize WebAssembly:', error);
      throw error;
    }
  }
  
  /**
   * Try multiple methods to retrieve the key from WebAssembly
   */
  private async retrieveKey(): Promise<string> {
    if (!this.module) {
      throw new Error('WebAssembly module not initialized');
    }
    
    let key = '';
    
    // Try Method 1: Get key byte by byte (most reliable)
    try {
      key = this.getKeyByteByByte();
      if (key.length === 32) {
        return key;
      }
    } catch (error) {
      console.warn('Failed to get key byte by byte:', error);
    }
    
    // Try Method 2: Get encrypted key and XOR decrypt it
    try {
      const encryptedKey = this.module.UTF8ToString(this.module._getEncryptedKey());
      if (encryptedKey.length === 32) {
        key = this.xorDecrypt(encryptedKey);
        if (key.length === 32) {
          return key;
        }
      }
    } catch (error) {
      console.warn('Failed to get encrypted key:', error);
    }
    
    // Try Method 3: Get key bytes using malloc/getKeyBytes
    try {
      const bufferPtr = this.module._getKeyBytes();
      if (bufferPtr !== 0) {
        let keyBytes = '';
        for (let i = 0; i < 32; i++) {
          keyBytes += String.fromCharCode(this.module.HEAPU8[bufferPtr + i]);
        }
        this.module._free(bufferPtr);
        
        if (keyBytes.length === 32) {
          return keyBytes;
        }
      }
    } catch (error) {
      console.warn('Failed to get key bytes:', error);
    }
    
    throw new Error('Failed to retrieve key from WebAssembly');
  }
  
  /**
   * Get the key byte by byte (most reliable method)
   */
  private getKeyByteByByte(): string {
    if (!this.module) {
      throw new Error('WebAssembly module not initialized');
    }
    
    let key = '';
    const keyLength = this.module._getSecretKeyLength();
    
    // Get each byte individually
    for (let i = 0; i < keyLength; i++) {
      const byteValue = this.module._getKeyByteAt(i);
      if (byteValue === -1) {
        throw new Error(`Failed to get key byte at index ${i}`);
      }
      key += String.fromCharCode(byteValue);
    }
    
    return key;
  }
  
  /**
   * XOR decrypt a string
   */
  private xorDecrypt(input: string): string {
    if (!input) return '';
    
    let result = '';
    const keyLen = this.xorKey.length;
    
    for (let i = 0; i < input.length; i++) {
      result += String.fromCharCode(input.charCodeAt(i) ^ this.xorKey.charCodeAt(i % keyLen));
    }
    
    return result;
  }

  /**
   * Get the encryption key
   */
  async getSecretKey(): Promise<string> {
    await this.init();
    
    if (!this.secretKey) {
      throw new Error('Failed to retrieve encryption key');
    }
    
    return this.secretKey;
  }

  /**
   * Decrypt data using AES-128-CBC
   */
  async decrypt(encryptedData: string): Promise<string> {
    const key = await this.getSecretKey();
    return decryptAES128CBC(encryptedData, key);
  }
  
  /**
   * Test if the module is working correctly
   */
  async testModule(): Promise<{
    initialized: boolean;
    keyAvailable: boolean;
    keyLength: number;
  }> {
    try {
      await this.init();
      
      return {
        initialized: this.isInitialized,
        keyAvailable: !!this.secretKey,
        keyLength: this.secretKey?.length || 0
      };
    } catch (error) {
      return {
        initialized: false,
        keyAvailable: false,
        keyLength: 0
      };
    }
  }
}

// Create and export singleton instance
export const wasmCrypto = new WasmCrypto();
/**
 * Utility functions for cryptography and YouTube URL parsing
 */

/**
 * Extracts YouTube video ID from various URL formats
 * @param url The YouTube URL to parse
 * @returns The video ID or null if no valid ID found
 */
export function extractYouTubeId(url: string): string | null {
    if (!url) return null;
  
    // Handle regular YouTube URLs
    const regExp = /^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??v?=?([^#&?]*).*/;
    const match = url.match(regExp);
    if (match && match[7] && match[7].length === 11) {
      return match[7];
    }
  
    // Handle shortened youtu.be URLs
    const shortRegExp = /^.*(youtu.be\/)(.*)/;
    const shortMatch = url.match(shortRegExp);
    if (shortMatch && shortMatch[2] && shortMatch[2].length === 11) {
      return shortMatch[2];
    }
  
    // Handle YouTube Shorts URLs
    const shortsRegExp = /^.*((youtube.com\/shorts\/))(.*)/;
    const shortsMatch = url.match(shortsRegExp);
    if (shortsMatch && shortsMatch[3] && shortsMatch[3].length === 11) {
      return shortsMatch[3];
    }
  
    return null;
  }
  
  /**
   * Decrypts AES-128-CBC encrypted data
   * @param encryptedData The encrypted data string (base64 encoded)
   * @param key The decryption key (hexadecimal string)
   * @returns A promise that resolves to the decrypted string
   */
  export async function decryptAES128CBC(encryptedData: string, key: string): Promise<string> {
    try {
      if (!encryptedData) throw new Error("No encrypted data provided");
      if (!key || key.length !== 32) throw new Error(`Invalid key format (length: ${key?.length || 0})`);
      
      // Convert key to proper format (hex string to ArrayBuffer)
      const keyBytes = hexStringToUint8Array(key);
      
      // Parse the encrypted data (assuming base64 encoding)
      const encryptedBytes = base64ToUint8Array(encryptedData);
      
      // Extract IV (first 16 bytes) and actual ciphertext
      if (encryptedBytes.length <= 16) {
        throw new Error("Encrypted data too short");
      }
      
      const iv = encryptedBytes.slice(0, 16);
      const ciphertext = encryptedBytes.slice(16);
      
      // Import the key
      const cryptoKey = await window.crypto.subtle.importKey(
        'raw',
        keyBytes,
        { name: 'AES-CBC', length: 128 },
        false,
        ['decrypt']
      );
      
      // Decrypt the data
      const decryptedBytes = await window.crypto.subtle.decrypt(
        { name: 'AES-CBC', iv },
        cryptoKey,
        ciphertext
      );
      
      // Convert the decrypted bytes to a string
      const decryptedText = new TextDecoder().decode(decryptedBytes);
      
      return decryptedText;
    } catch (error) {
      console.error('AES decryption error:', error);
      throw new Error(`Decryption failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  
  /**
   * Converts a hex string to Uint8Array
   * @param hexString The hex string to convert
   * @returns The resulting Uint8Array
   */
  export function hexStringToUint8Array(hexString: string): Uint8Array {
    // Remove any non-hex characters (like spaces)
    hexString = hexString.replace(/[^0-9A-Fa-f]/g, '');
    
    // Ensure we have an even number of characters
    if (hexString.length % 2 !== 0) {
      hexString = '0' + hexString;
    }
    
    const bytes = new Uint8Array(hexString.length / 2);
    
    for (let i = 0; i < hexString.length; i += 2) {
      bytes[i / 2] = parseInt(hexString.substring(i, i + 2), 16);
    }
    
    return bytes;
  }
  
  /**
   * Converts a base64 string to Uint8Array
   * @param base64String The base64 string to convert
   * @returns The resulting Uint8Array
   */
  export function base64ToUint8Array(base64String: string): Uint8Array {
    try {
      // First, get a binary string from the base64 string
      const binaryString = atob(base64String);
      const bytes = new Uint8Array(binaryString.length);
      
      // Convert each character to its char code
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      
      return bytes;
    } catch (error) {
      console.error("Base64 conversion error:", error);
      throw new Error("Invalid base64 data");
    }
  }
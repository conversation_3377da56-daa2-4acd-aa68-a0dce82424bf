"use client";

import { useState, useRef, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Download, Loader2, XCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { youtubeClient, DownloadFormat } from "@/lib/youtube-client";
import { mp3juiceClient } from "@/lib/mp3juice-client";
import { useTranslations } from 'next-intl';

interface DownloadButtonProps {
  selectedFormat: DownloadFormat | null;
  videoTitle: string;
  videoId: string;
}

export function DownloadButton({ selectedFormat, videoTitle, videoId }: DownloadButtonProps) {
  const t = useTranslations('downloadButton');
  
  const [isDownloading, setIsDownloading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalDownloaded, setTotalDownloaded] = useState(0);
  
  const abortControllerRef = useRef<AbortController | null>(null);
  
  // Format bytes to human-readable format
  const formatBytes = (bytes: number, decimals = 2) => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  };

  // Check if an error is likely a CORS error
  const isCorsError = (error: any): boolean => {
    if (!error) return false;
    
    // Network errors caused by CORS often have these characteristics
    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      return true;
    }
    
    // Some browsers provide more specific error messages
    if (error.message && (
        error.message.includes('CORS') || 
        error.message.includes('cross-origin') || 
        error.message.includes('Cross-Origin')
      )) {
      return true;
    }
    
    // Check for specific error codes that often indicate CORS issues
    if (error.name === 'NetworkError' || error.name === 'SecurityError') {
      return true;
    }
    
    return false;
  };

  // Function to open direct download in new tab and stop download process
  const openDirectDownload = useCallback((url: string) => {
    window.open(url, '_blank');
    // Stop the download process and loader
    setIsDownloading(false);
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  // Advanced parallel chunk download implementation
  const directDownload = useCallback(async (url: string) => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    abortControllerRef.current = new AbortController();
    const signal = abortControllerRef.current.signal;
    
    try {
      // First check if the server supports range requests
      const headResponse = await fetch(url, {
        method: 'HEAD',
        signal,
        mode: 'cors',
        cache: 'no-store',
        credentials: 'same-origin'
      });
      
      const contentLength = Number(headResponse.headers.get('Content-Length') || '0');
      const acceptsRanges = headResponse.headers.get('Accept-Ranges') === 'bytes';
      const contentType = headResponse.headers.get('Content-Type') || 'application/octet-stream';
      
      // If range requests are supported and content size is known and significant
      if (acceptsRanges && contentLength > 5 * 1024 * 1024) { // Only use chunks for files > 5MB
        return downloadInChunks(url, contentLength, contentType, setTotalDownloaded, signal);
      } 
      
      // Otherwise fall back to a standard fetch
      const response = await fetch(url, { 
        signal,
        mode: 'cors',
        priority: 'high' as any,
        cache: 'no-store',
        credentials: 'same-origin'
      });
      
      if (!response.ok) {
        throw new Error(`Download failed: ${response.status}`);
      }
      
      // Check if browser supports streams API
      if (response.body && window.WritableStream) {
        return streamToBlob(response, setTotalDownloaded, signal);
      } else {
        // Fallback for browsers without stream support
        return legacyDownload(response, setTotalDownloaded, signal);
      }
    } catch (error) {
      if (error instanceof DOMException && error.name === 'AbortError') {
        return null;
      }
      
      // Check if this is likely a CORS error and immediately open in new tab
      if (isCorsError(error)) {
        openDirectDownload(url);
        return null;
      }
      
      throw error;
    }
  }, [openDirectDownload]);

  // Truly parallel chunk download implementation
  const downloadInChunks = async (
    url: string,
    contentLength: number,
    contentType: string,
    updateProgress: (bytes: number) => void,
    signal: AbortSignal
  ): Promise<Blob | null> => {
    // Log the start of parallel downloading
    console.log(`Starting parallel download of ${contentLength} bytes`);
    
    // Determine optimal chunk size and count
    const MAX_PARALLEL_CHUNKS = 5; // Maximum parallel downloads
    
    // Calculate optimal chunk size based on file size
    // Smaller files -> smaller chunks, larger files -> larger chunks
    let chunkSize = 1 * 1024 * 1024; // 1MB default
    if (contentLength > 50 * 1024 * 1024) {
      chunkSize = 5 * 1024 * 1024; // 5MB for large files
    } else if (contentLength > 20 * 1024 * 1024) {
      chunkSize = 2 * 1024 * 1024; // 2MB for medium files
    }
    
    const totalChunks = Math.ceil(contentLength / chunkSize);
    const chunks: (Uint8Array | null)[] = new Array(totalChunks).fill(null);
    
    // Shared state for progress tracking
    const downloadState = {
      receivedLength: 0,
      completedChunks: 0,
      activeDownloads: 0,
      aborted: false
    };
    
    return new Promise<Blob | null>(async (resolve, reject) => {
      // Check for abort signal
      if (signal.aborted) {
        resolve(null);
        return;
      }
      
      // Set up abort handler
      signal.addEventListener('abort', () => {
        downloadState.aborted = true;
        resolve(null);
      });
      
      // Function to download a specific chunk
      const downloadChunk = async (chunkIndex: number): Promise<void> => {
        if (downloadState.aborted || signal.aborted) return;
        
        const start = chunkIndex * chunkSize;
        const end = Math.min(start + chunkSize - 1, contentLength - 1);
        
        try {
          downloadState.activeDownloads++;
          
          // Log chunk download
          console.log(`Downloading chunk ${chunkIndex+1}/${totalChunks} (bytes ${start}-${end})`);
          
          // Download this chunk with a range request
          const response = await fetch(url, {
            headers: { Range: `bytes=${start}-${end}` },
            mode: 'cors',
            cache: 'no-store',
            signal
          });
          
          if (!response.ok && response.status !== 206) {
            throw new Error(`Chunk download failed: ${response.status}`);
          }
          
          // Get the data from this chunk
          const buffer = await response.arrayBuffer();
          const chunkData = new Uint8Array(buffer);
          
          // Store in our array
          chunks[chunkIndex] = chunkData;
          
          // Update progress
          downloadState.receivedLength += chunkData.length;
          downloadState.completedChunks++;
          updateProgress(downloadState.receivedLength);
          
          console.log(`Chunk ${chunkIndex+1} complete. Total: ${downloadState.completedChunks}/${totalChunks}`);
        } catch (error) {
          if (signal.aborted || downloadState.aborted) {
            return; // Just return on abort
          }
          throw error; // Re-throw other errors
        } finally {
          downloadState.activeDownloads--;
        }
      };
      
      // Download scheduler - keeps MAX_PARALLEL_CHUNKS downloads active
      const downloadScheduler = async (): Promise<void> => {
        let nextChunkIndex = 0;
        
        const startNextDownload = async () => {
          if (nextChunkIndex >= totalChunks || downloadState.aborted || signal.aborted) {
            return;
          }
          
          const chunkIndex = nextChunkIndex++;
          try {
            await downloadChunk(chunkIndex);
          } catch (error) {
            if (!(error instanceof DOMException && error.name === 'AbortError')) {
              downloadState.aborted = true;
              reject(error);
            }
          }
          
          // Start another download if there are more chunks
          if (nextChunkIndex < totalChunks && !downloadState.aborted && !signal.aborted) {
            startNextDownload();
          }
        };
        
        // Start initial parallel downloads
        const initialDownloads = Math.min(MAX_PARALLEL_CHUNKS, totalChunks);
        console.log(`Starting ${initialDownloads} parallel downloads`);
        
        const initialPromises = [];
        for (let i = 0; i < initialDownloads; i++) {
          initialPromises.push(startNextDownload());
        }
        
        await Promise.all(initialPromises);
      };
      
      try {
        // Start the download process
        await downloadScheduler();
        
        // Wait until all chunks are downloaded
        while (downloadState.completedChunks < totalChunks && !downloadState.aborted && !signal.aborted) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        // If aborted, return null
        if (downloadState.aborted || signal.aborted) {
          resolve(null);
          return;
        }
        
        console.log(`All ${totalChunks} chunks downloaded. Combining...`);
        
        // Calculate total size (some chunks might be smaller than expected)
        let totalSize = 0;
        chunks.forEach(chunk => {
          if (chunk) totalSize += chunk.length;
        });
        
        // Create the final array buffer
        const mergedArray = new Uint8Array(totalSize);
        let position = 0;
        
        // Copy each chunk into the final array
        chunks.forEach(chunk => {
          if (chunk) {
            mergedArray.set(chunk, position);
            position += chunk.length;
          }
        });
        
        // Create and return the blob
        const blob = new Blob([mergedArray], { type: contentType });
        console.log(`Download complete: ${totalSize} bytes`);
        resolve(blob);
      } catch (error) {
        console.error('Download error:', error);
        if (error instanceof DOMException && error.name === 'AbortError') {
          resolve(null);
        } else {
          reject(error);
        }
      }
    });
  };

  // Modern streaming download implementation (for non-chunked downloads)
  const streamToBlob = async (
    response: Response, 
    updateProgress: (bytes: number) => void,
    signal: AbortSignal
  ): Promise<Blob | null> => {
    const contentLength = Number(response.headers.get('Content-Length')) || 0;
    let receivedLength = 0;
    
    // Use a ReadableStream with a larger buffer size for better performance
    const reader = response.body!.getReader();
    const chunks: Uint8Array[] = [];
    
    // Create a buffer size of 1MB for more efficient processing
    const BUFFER_SIZE = 1024 * 1024;
    
    return new Promise<Blob | null>(async (resolve, reject) => {
      // Check for abort signal
      if (signal.aborted) {
        resolve(null);
        return;
      }
      
      // Add abort listener
      signal.addEventListener('abort', () => resolve(null));
      
      try {
        // Process chunks in larger batches
        while (true) {
          if (signal.aborted) {
            resolve(null);
            return;
          }
          
          const { done, value } = await reader.read();
          
          if (done) {
            break;
          }
          
          chunks.push(value);
          receivedLength += value.length;
          
          // Update progress less frequently for better performance
          if (receivedLength % BUFFER_SIZE === 0 || 
              (contentLength > 0 && receivedLength === contentLength)) {
            updateProgress(receivedLength);
          }
        }
        
        // Final progress update
        updateProgress(receivedLength);
        
        // Create blob with optimized MIME type detection
        const mimeType = response.headers.get('Content-Type') || 'application/octet-stream';
        const blob = new Blob(chunks, { type: mimeType });
        resolve(blob);
      } catch (error) {
        if (error instanceof DOMException && error.name === 'AbortError') {
          resolve(null);
        } else {
          reject(error);
        }
      }
    });
  };

  // Legacy download for browsers without stream support
  const legacyDownload = async (
    response: Response, 
    updateProgress: (bytes: number) => void,
    signal: AbortSignal
  ): Promise<Blob | null> => {
    // For browsers without ReadableStream support
    const reader = response.body?.getReader();
    if (!reader) throw new Error("Cannot read response body");
    
    let receivedLength = 0;
    const chunks: Uint8Array[] = [];
    
    return new Promise<Blob | null>(async (resolve, reject) => {
      // Check for abort signal
      if (signal.aborted) {
        resolve(null);
        return;
      }
      
      // Add abort listener
      signal.addEventListener('abort', () => resolve(null));
      
      try {
        // Stream the download in standard way
        while (true) {
          if (signal.aborted) {
            resolve(null);
            return;
          }
          
          const { done, value } = await reader.read();
          
          if (done) {
            break;
          }
          
          chunks.push(value);
          receivedLength += value.length;
          updateProgress(receivedLength);
        }
        
        // Combine all chunks
        const mergedChunks = new Uint8Array(receivedLength);
        let position = 0;
        for (const chunk of chunks) {
          mergedChunks.set(chunk, position);
          position += chunk.length;
        }
        
        resolve(new Blob([mergedChunks], { type: 'application/octet-stream' }));
      } catch (error) {
        if (error instanceof DOMException && error.name === 'AbortError') {
          resolve(null);
        } else {
          reject(error);
        }
      }
    });
  };

  const handleCancelDownload = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setIsDownloading(false);
    setError(null);
    setTotalDownloaded(0);
  }, []);
  
  const handleDownload = useCallback(async () => {
    if (!selectedFormat || !videoId) return;

    setIsDownloading(true);
    setError(null);
    setTotalDownloaded(0);

    try {
      // Create a safe filename (remove special characters and replace spaces with underscores)
      const safeTitle = videoTitle.replace(/[^\w\s]/gi, '').replace(/\s+/g, '_');
      
      // Get download URL - check if format already has URL (MP3Juice) or needs to be fetched (YouTube)
      let downloadUrl: string;

      if (selectedFormat.url) {
        // MP3Juice format already has direct URL
        downloadUrl = selectedFormat.url;
      } else {
        // YouTube format needs URL to be fetched
        const downloadUrlPromise = youtubeClient.getDownloadUrl(videoId, selectedFormat);

        // Use Promise.race to implement a timeout
        const timeoutPromise = new Promise<null>((_, reject) => {
          setTimeout(() => reject(new Error(
            "Download timed out. Please try again."
          )), 15000);
        });

        const fetchedUrl = await Promise.race([downloadUrlPromise, timeoutPromise]) as string | null;

        if (!fetchedUrl) {
          throw new Error("Couldn't get download URL. Please try another format.");
        }

        downloadUrl = fetchedUrl;
      }
      
      // Download the file directly
      const downloadedBlob = await directDownload(downloadUrl);
      
      if (!downloadedBlob) {
        // Download was cancelled, failed, or opened in new tab due to CORS
        return;
      }
      
      // Determine appropriate file extension
      let fileExtension = selectedFormat.container || 
        (selectedFormat.type === 'audio' ? 'mp3' : 'mp4');
      
      // Create download link - optimized with faster approach
      const filename = `${safeTitle}.${fileExtension}`;
      
      // Use the newer download API if available for better performance
      if ('showSaveFilePicker' in window) {
        try {
          // @ts-ignore - TypeScript doesn't know about this API yet
          const fileHandle = await window.showSaveFilePicker({
            suggestedName: filename,
            types: [{
              description: 'Media file',
              accept: { 
                'video/mp4': ['.mp4'],
                'audio/mp3': ['.mp3'],
                'application/octet-stream': [`.${fileExtension}`] 
              }
            }]
          });
          
          // Get a writable stream to the file
          // @ts-ignore
          const writable = await fileHandle.createWritable();
          await writable.write(downloadedBlob);
          await writable.close();
          
          // Reset state immediately
          setIsDownloading(false);
          setTotalDownloaded(0);
          return;
        } catch (err) {
          // File System Access API failed, fall back to traditional method
          console.warn('File System Access API failed, using fallback', err);
        }
      }
      
      // Fallback to traditional download method
      const blobUrl = URL.createObjectURL(downloadedBlob);
      const a = document.createElement('a');
      a.href = blobUrl;
      a.download = filename;
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      URL.revokeObjectURL(blobUrl);
      document.body.removeChild(a);
      
      // Reset state after a short delay
      setTimeout(() => {
        setIsDownloading(false);
        setTotalDownloaded(0);
      }, 500); // Reduced delay

    } catch (err) {
      // Don't show CORS errors - they are handled by opening in a new tab
      if (isCorsError(err)) {
        setIsDownloading(false);
        return;
      }
      
      // User-friendly error messages for other errors
      let userFriendlyError = t('errors.default');
      
      if (err instanceof Error) {
        const errorMsg = err.message.toLowerCase();
        
        if (errorMsg.includes('network') || errorMsg.includes('fetch') || errorMsg.includes('connection')) {
          userFriendlyError = t('errors.network');
        } 
        else if (errorMsg.includes('memory') || errorMsg.includes('out of memory')) {
          userFriendlyError = t('errors.memory');
        }
        else if (errorMsg.includes('abort') || errorMsg.includes('cancel')) {
          userFriendlyError = t('errors.cancelled');
        }
        else if (errorMsg.includes('url')) {
          userFriendlyError = t('errors.url');
        }
        else if (errorMsg.includes('timeout')) {
          userFriendlyError = t('errors.timeout');
        }
      }
      
      setError(userFriendlyError);
      setIsDownloading(false);
      setTotalDownloaded(0);
    }
  }, [selectedFormat, videoTitle, videoId, directDownload, t]);

  return (
    <div className="space-y-4">
      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex flex-col">
        <Button
          onClick={handleDownload}
          disabled={!selectedFormat || isDownloading}
          className="w-full py-6"
          size="lg"
          variant="default"
        >
          {isDownloading ? (
            <>
              <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              {t('downloading')}
            </>
          ) : (
            <>
              <Download className="mr-2 h-5 w-5" />
              {selectedFormat?.label
                ? t('downloadWithFormat', { format: selectedFormat.label })
                : t('download')
              }
            </>
          )}
        </Button>

        {isDownloading && (
          <>
            {/* Simple download indicator showing only the amount downloaded */}
            <div className="mt-5 text-center">
              <div className="text-xl font-bold">
                {t('downloaded', { size: formatBytes(totalDownloaded) })}
              </div>
            </div>
            
            {/* Cancel button aligned to the center */}
            <div className="flex justify-center mt-4">
              <Button 
                variant="outline" 
                size="default"
                onClick={handleCancelDownload}
                className="text-red-500 hover:text-red-700 hover:bg-red-50"
              >
                <XCircle className="h-4 w-4 mr-2" />
                {t('cancel')}
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
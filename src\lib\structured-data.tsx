/**
 * Helper functions to generate structured data (JSON-LD) for different pages
 */

// Website schema for the main site
export function generateWebsiteSchema(title: string, description: string, url: string = "https://ytmate.net") {
    return {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": title,
      "description": description,
      "url": url,
      "potentialAction": {
        "@type": "SearchAction",
        "target": {
          "@type": "EntryPoint",
          "urlTemplate": `${url}/search?q={search_term_string}`
        },
        "query-input": "required name=search_term_string"
      }
    };
  }
  
  // SoftwareApplication schema for the YouTube converter tool
  export function generateSoftwareSchema(title: string, description: string) {
    return {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": title,
      "description": description,
      "applicationCategory": "MultimediaApplication",
      "operatingSystem": "Web",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      }
    };
  }
  
  // Organization schema for the site publisher
  export function generateOrganizationSchema(name: string, url: string = "https://ytmate.net", logo: string = "https://ytmate.net/logo.png") {
    return {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": name,
      "url": url,
      "logo": logo
    };
  }
  
  // VideoObject schema for YouTube to MP4 page
  export function generateVideoObjectSchema(name: string, description: string, thumbnailUrl: string = "https://ytmate.net/video-thumbnail.jpg") {
    return {
      "@context": "https://schema.org",
      "@type": "VideoObject",
      "name": name,
      "description": description,
      "thumbnailUrl": thumbnailUrl,
      "uploadDate": new Date().toISOString().split('T')[0],
      "contentUrl": "https://ytmate.net/youtube-to-mp4"
    };
  }
  
  // AudioObject schema for YouTube to MP3 page
  export function generateAudioObjectSchema(name: string, description: string) {
    return {
      "@context": "https://schema.org",
      "@type": "AudioObject",
      "name": name,
      "description": description,
      "encodingFormat": "mp3",
      "contentUrl": "https://ytmate.net/youtube-to-mp3"
    };
  }
  
  // FAQPage schema for FAQ sections
  export function generateFaqSchema(questions: Array<{question: string, answer: string}>) {
    return {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": questions.map(q => ({
        "@type": "Question",
        "name": q.question,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": q.answer
        }
      }))
    };
  }
  
  // Collection of all schemas for the homepage
  export function generateHomePageSchemas(title: string, description: string, publisher: string) {
    return [
      generateWebsiteSchema(title, description),
      generateSoftwareSchema(title, description),
      generateOrganizationSchema(publisher)
    ];
  }
  
  // Collection of all schemas for the MP3 page
  export function generateMp3PageSchemas(title: string, description: string, publisher: string, faqItems: Array<{question: string, answer: string}>) {
    return [
      generateWebsiteSchema(title, description),
      generateAudioObjectSchema(title, description),
      generateOrganizationSchema(publisher),
      generateFaqSchema(faqItems)
    ];
  }
  
  // Collection of all schemas for the MP4 page
  export function generateMp4PageSchemas(title: string, description: string, publisher: string, faqItems: Array<{question: string, answer: string}>) {
    return [
      generateWebsiteSchema(title, description),
      generateVideoObjectSchema(title, description),
      generateOrganizationSchema(publisher),
      generateFaqSchema(faqItems)
    ];
  }
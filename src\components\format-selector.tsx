"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Volume2, FileVideo } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { DownloadFormat } from "@/lib/youtube-client";
import { useTranslations } from 'next-intl';

interface FormatSelectorProps {
  videoFormats: DownloadFormat[];
  audioFormats: DownloadFormat[];
  onFormatSelect: (format: DownloadFormat) => void;
  selectedFormatId: number | null;
}

export function FormatSelector({
  videoFormats,
  audioFormats,
  onFormatSelect,
  selectedFormatId,
}: FormatSelectorProps) {
  const t = useTranslations('formatSelector');
  const [selectedFormat, setSelectedFormat] = useState<DownloadFormat | null>(null);

  // Find the initial selected format when component mounts or when selectedFormatId changes
  useEffect(() => {
    if (!selectedFormatId) return;
    
    const videoFormat = videoFormats.find(f => f.quality === selectedFormatId);
    if (videoFormat) {
      setSelectedFormat(videoFormat);
      return;
    }
    
    const audioFormat = audioFormats.find(f => f.quality === selectedFormatId);
    if (audioFormat) {
      setSelectedFormat(audioFormat);
      return;
    }
  }, [selectedFormatId, videoFormats, audioFormats]);

  // Handle format selection
  const handleFormatSelect = (formatId: string) => {
    // Parse the format id to get type and quality
    const [type, qualityStr] = formatId.split('-');
    const quality = parseInt(qualityStr, 10);
    
    // Find the selected format
    let selectedFormat: DownloadFormat | undefined;
    
    if (type === 'video') {
      selectedFormat = videoFormats.find(f => f.quality === quality);
    } else {
      selectedFormat = audioFormats.find(f => f.quality === quality);
    }
    
    if (selectedFormat) {
      onFormatSelect(selectedFormat);
      setSelectedFormat(selectedFormat);
    }
  };

  return (
    <Card className="my-4 shadow-sm">
      <CardContent className="p-4">
        <Select 
          onValueChange={handleFormatSelect}
          value={selectedFormat ? `${selectedFormat.type}-${selectedFormat.quality}` : undefined}
        >
          <SelectTrigger className="w-full bg-white">
            <SelectValue placeholder={t('placeholder')} />
          </SelectTrigger>
          <SelectContent>
            {videoFormats.length > 0 && (
              <SelectGroup>
                <SelectLabel className="text-sm font-medium">{t('videoFormats')}</SelectLabel>
                {videoFormats.map((format) => (
                  <SelectItem 
                    key={`video-${format.quality}`} 
                    value={`video-${format.quality}`}
                    className="flex items-center"
                  >
                    <div className="flex items-center gap-2">
                      <FileVideo className="h-4 w-4 text-muted-foreground" />
                      <span>{format.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectGroup>
            )}
            
            {audioFormats.length > 0 && (
              <SelectGroup>
                <SelectLabel className="text-sm font-medium">{t('audioFormats')}</SelectLabel>
                {audioFormats.map((format) => (
                  <SelectItem 
                    key={`audio-${format.quality}`} 
                    value={`audio-${format.quality}`}
                    className="flex items-center"
                  >
                    <div className="flex items-center gap-2">
                      <Volume2 className="h-4 w-4 text-muted-foreground" />
                      <span>{format.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectGroup>
            )}
          </SelectContent>
        </Select>
        
        {selectedFormat && (
          <div className="mt-3 text-sm text-muted-foreground">
            <p className="flex items-center gap-2">
              {selectedFormat.type === 'audio' ? (
                <Volume2 className="h-4 w-4" />
              ) : (
                <FileVideo className="h-4 w-4" />
              )}
              <span>{t('selectedFormat.type')}: {selectedFormat.type === 'audio' ? t('selectedFormat.audio') : t('selectedFormat.video')}</span>
              <span>•</span>
              <span>{t('selectedFormat.quality')}: {selectedFormat.label}</span>
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
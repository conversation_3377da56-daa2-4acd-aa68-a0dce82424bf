
module.exports = {
    siteUrl: 'https://ytmate.net',
    generateRobotsTxt: true,
    i18n: {
      locales: ['en', 'es', 'de', 'ja', 'it', 'fr', 'pt', 'ru', 'tr', 'zh', 'pl'],
      defaultLocale: 'en',
    },
    exclude: [],
  
    additionalPaths: async (config) => {
      const staticPages = [
        '/', // Home
        '/youtube-to-mp3',
        '/youtube-to-mp4',
      ];
  
      // Generate all localized variants of each page
      const all = [];
  
      for (const locale of config.i18n.locales) {
        for (const page of staticPages) {
          all.push({
            loc:
              page === '/' && locale === config.i18n.defaultLocale
                ? `/${locale}` // home
                : `/${locale}${page}`,
            changefreq: 'weekly',
            priority: 0.7,
            lastmod: new Date().toISOString(),
          });
        }
      }
  
      return all;
    },
  };
  
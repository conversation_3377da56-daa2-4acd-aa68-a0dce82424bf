/**
 * YouTube client with secure WebAssembly-based encryption
 * Uses multiple key retrieval methods for greater reliability
 */
import axios, { AxiosRequestConfig } from "axios";
import { extractYouTubeId } from "./crypto";
import { wasmCrypto } from "./wasm-crypto";

// Define interfaces
export interface AudioFormat {
  quality: number;
  url: string | null;
  label: string;
}

export interface VideoFormat {
  height: number;
  width: number;
  url: string | null;
  quality: number;
  label: string;
  default_selected: number;
}

export interface ThumbnailFormat {
  label: string;
  quality: string;
  value: string;
  url: string;
}

export interface VideoInfo {
  key: string;
  url: string;
  title: string;
  titleSlug: string;
  thumbnail: string;
  duration: number;
  durationLabel: string;
  audio_formats: AudioFormat[];
  video_formats: VideoFormat[];
  thumbnail_formats: ThumbnailFormat[];
  default_selected: number;
  fromCache?: boolean;
  videoId?: string;
}

export interface DownloadFormat {
  quality: number;
  label: string;
  url: string | null;
  type: 'video' | 'audio';
  container: string;
  height?: number;
  width?: number;
  sizeInMB?: number;
}

interface RequestResponse {
  status: boolean;
  code: number;
  data: any;
}

/**
 * YouTube client with WebAssembly-based encryption
 */
class YouTubeClient {
  private readonly api = {
    base: "/api",
    cdn: "/get-cdn",
    info: "/v2/info",
    download: "/download",
  };

  private readonly headers = {
    accept: "*/*",
    "content-type": "application/json"
  };

  /**
   * Decrypt data using the WebAssembly module
   */
  private async decrypt(enc: string): Promise<any> {
    try {
      // Decrypt the data
      const decryptedText = await wasmCrypto.decrypt(enc);
      
      // Parse JSON
      try {
        return JSON.parse(decryptedText);
      } catch (parseError) {
        console.error("JSON parse error:", parseError);
        throw new Error("Failed to parse decrypted data as JSON");
      }
    } catch (error) {
      console.error("Decryption failed:", error);
      throw new Error(`Failed to decrypt video information: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Extract video ID from a YouTube URL
   */
  private extractVideoId(url: string): string | null {
    return extractYouTubeId(url);
  }

  /**
   * Make an HTTP request to the API
   */
  private async request(endpoint: string, data: any = {}, method: string = "post"): Promise<RequestResponse> {
    try {
      const url = endpoint.startsWith("http") ? endpoint : `${this.api.base}${endpoint}`;
      
      const config: AxiosRequestConfig = {
        method,
        url,
        headers: this.headers,
      };
      
      if (method.toLowerCase() === "post") {
        config.data = data;
      } else {
        config.params = data;
      }
      
      const response = await axios(config);
      
      return {
        status: true,
        code: response.status,
        data: response.data,
      };
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error("API request error:", {
          url: error.config?.url,
          status: error.response?.status,
          statusText: error.response?.statusText,
        });
      } else {
        console.error("Request error:", error);
      }
      throw new Error(`Request failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get CDN information
   */
  private async getCDN(): Promise<RequestResponse> {
    try {
      const response = await this.request(this.api.cdn, {}, "get");
      if (!response.status) throw new Error("Failed to get CDN");
      return response;
    } catch (error) {
      console.error("Error getting CDN:", error);
      // For testing, return a mock CDN
      return {
        status: true,
        code: 200,
        data: {
          cdn: "api.ytapis.com"
        }
      };
    }
  }

  /**
   * Generate MP3 format options
   */
  private generateMP3Formats(baseFormat: AudioFormat): AudioFormat[] {
    if (!baseFormat) return [];
    
    return [
      { quality: 320, url: null, label: "MP3 320kbps" },
      { quality: 256, url: null, label: "MP3 256kbps" },
      { quality: 192, url: null, label: "MP3 192kbps" },
      { quality: 128, url: null, label: "MP3 128kbps" },
      { quality: 96, url: null, label: "MP3 96kbps" },
    ];
  }

  /**
   * Initialize and test WebAssembly
   * This uses a simpler approach that should be more reliable
   */
  private async initWebAssembly(): Promise<void> {
    try {
      // Try to initialize the WebAssembly module
      await wasmCrypto.init();
      
      // Test if the key is available and valid
      const key = await wasmCrypto.getSecretKey();
      if (!key || key.length !== 32) {
        throw new Error(`Invalid key length: ${key ? key.length : 0}, expected 32`);
      }
      
    } catch (error) {
      console.error("WebAssembly initialization failed:", error);
      throw new Error(`Failed to initialize WebAssembly: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get video information from a YouTube URL
   */
  public async getVideoInfo(url: string): Promise<VideoInfo> {
    try {
      // Make sure WebAssembly is initialized
      await this.initWebAssembly();
      
      // Extract video ID
      const videoId = this.extractVideoId(url);
      if (!videoId) throw new Error("Invalid YouTube URL");

      // Get CDN information
      const cdnResult = await this.getCDN();
      const cdn = cdnResult.data.cdn;
      
      // Get video information
      const infoResult = await this.request(
        `https://${cdn}${this.api.info}`,
        {
          url: `https://www.youtube.com/watch?v=${videoId}`,
        }
      );
      
      if (!infoResult.status) throw new Error("Failed to get video info");
      
      // Check if data exists in the response
      if (!infoResult.data || !infoResult.data.data) {
        throw new Error("Invalid API response format");
      }
      
      // Decrypt the video information
      const decrypted = await this.decrypt(infoResult.data.data);
      
      // Add enhanced MP3 formats if audio formats exist
      if (decrypted.audio_formats && decrypted.audio_formats.length > 0) {
        decrypted.audio_formats = this.generateMP3Formats(decrypted.audio_formats[0]);
      }
      
      // Add videoId for convenience
      decrypted.videoId = videoId;
      
      return decrypted as VideoInfo;
    } catch (error) {
      throw new Error(`Failed to get video info: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get a download URL for a specific format
   */
  public async getDownloadUrl(videoId: string, format: DownloadFormat): Promise<string> {
    try {
      // Make sure WebAssembly is initialized
      await this.initWebAssembly();
      
      if (!videoId) throw new Error("Video ID is required");
      if (!format) throw new Error("Format is required");
      
      // If URL is already available, return it directly
      if (format.url) return format.url;
      
      const cdnResult = await this.getCDN();
      const cdn = cdnResult.data.cdn;
      
      // First get the video info to retrieve the key
      const infoResult = await this.request(
        `https://${cdn}${this.api.info}`,
        {
          url: `https://www.youtube.com/watch?v=${videoId}`,
        }
      );
      
      if (!infoResult.status || !infoResult.data || !infoResult.data.data) {
        throw new Error("Failed to get video info");
      }
      
      // Decrypt the video information to get the key
      const decrypted = await this.decrypt(infoResult.data.data);
      
      // Now request the download URL
      const downloadResult = await this.request(
        `https://${cdn}${this.api.download}`,
        {
          id: videoId,
          downloadType: format.type === 'audio' ? "audio" : "video",
          quality: format.quality.toString(),
          key: decrypted.key,
        }
      );
      
      if (!downloadResult.status || !downloadResult.data || !downloadResult.data.data) {
        throw new Error("Failed to get download URL");
      }
      
      return downloadResult.data.data.downloadUrl;
    } catch (error) {
      console.error("Error getting download URL:", error);
      throw new Error(`Failed to get download URL: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}

// Export a singleton instance
export const youtubeClient = new YouTubeClient();
"use client";

import { useEffect, useState } from 'react';

export function DesktopHeaderScript() {
  const [isProduction, setIsProduction] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);

  useEffect(() => {
    // Check if we're in production environment
    setIsProduction(process.env.NODE_ENV === 'production');
    
    // Check if device is desktop/laptop (screen width >= 1024px)
    const checkIsDesktop = () => {
      setIsDesktop(window.innerWidth >= 1024);
    };
    
    // Initial check
    checkIsDesktop();
    
    // Listen for window resize
    window.addEventListener('resize', checkIsDesktop);
    
    return () => {
      window.removeEventListener('resize', checkIsDesktop);
    };
  }, []);

  useEffect(() => {
    // Only load script for desktop devices in production
    if (isProduction && isDesktop) {
      const loadDesktopScript = () => {
        setTimeout(() => {
          // Check if script is already loaded to prevent duplicates
          const existingScript = document.querySelector('script[src*="pl26876403.profitableratecpm.com"]');
          if (!existingScript) {
            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = '//pl26876403.profitableratecpm.com/00/62/e3/0062e3ee511a200af142b77fe2483102.js';
            document.head.appendChild(script);
          }
        }, 2000);
      };

      // Check if page is already loaded
      if (document.readyState === 'complete') {
        loadDesktopScript();
      } else {
        // Wait for page to load completely
        window.addEventListener('load', loadDesktopScript, { once: true });
      }
    }
  }, [isProduction, isDesktop]);

  // This component doesn't render anything visible
  return null;
}
